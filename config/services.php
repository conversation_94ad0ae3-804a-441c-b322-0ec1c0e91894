<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'csl-99' => [
        'host' => env('CSL_99_SSH_HOST'),
        'user' => env('CSL_99_SSH_USER'),
        'private_key' => env('CSL_99_SSH_PRIVATE_KEY_PATH'),
        'port' => env('CSL_99_SSH_PORT', 22),
    ],

    'facebook' => [
        'page_id' => env('FACEBOOK_PAGE_ID'),
        'app_id' => env('FACEBOOK_APP_ID'),
    ],

    'zalo' => [
        'oa_id' => env('ZALO_OA_ID'),
        'app_id' => env('ZALO_APP_ID'),
    ],

];
