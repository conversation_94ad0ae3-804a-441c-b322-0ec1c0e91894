// Template Favorites Global JavaScript

window.toggleTemplateFavorite = function(templateId, buttonElement) {
    const heartIcon = buttonElement.querySelector('svg');
    const isCurrentlyFavorited = buttonElement.dataset.isFavorited === 'true';
    
    // Disable button during request
    buttonElement.disabled = true;
    buttonElement.classList.add('animate-pulse');
    
    fetch(`/template-favorites/${templateId}/toggle`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update button state
            buttonElement.dataset.isFavorited = data.is_favorited ? 'true' : 'false';
            
            if (data.is_favorited) {
                // Added to favorites
                heartIcon.classList.remove('text-gray-400');
                heartIcon.classList.add('text-red-500', 'fill-current');
                
                // Heart beat animation
                heartIcon.classList.add('animate-bounce');
                setTimeout(() => {
                    heartIcon.classList.remove('animate-bounce');
                }, 600);
                
            } else {
                // Removed from favorites
                heartIcon.classList.remove('text-red-500', 'fill-current');
                heartIcon.classList.add('text-gray-400');
            }
            
            // Show success message
            showMiniToast(data.message, 'success');
            
        } else {
            showMiniToast(data.message || 'Có lỗi xảy ra', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMiniToast('Có lỗi xảy ra', 'error');
    })
    .finally(() => {
        // Re-enable button
        buttonElement.disabled = false;
        buttonElement.classList.remove('animate-pulse');
    });
};

function showMiniToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300 text-sm ${
        type === 'success' ? 'bg-green-500 text-white' : 
        type === 'error' ? 'bg-red-500 text-white' : 
        'bg-blue-500 text-white'
    }`;
    toast.textContent = message;
    
    document.body.appendChild(toast);
    
    // Show toast
    requestAnimationFrame(() => {
        toast.classList.remove('translate-x-full');
    });
    
    // Hide and remove toast
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 2000);
}
