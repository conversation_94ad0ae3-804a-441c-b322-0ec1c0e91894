@extends('layouts.default')

@section('content')
    <section class="py-12 md:py-16 bg-gray-50">
        <div class="container px-4 mx-auto">
            <div class="max-w-md mx-auto bg-white rounded-xl shadow-sm overflow-hidden">
                <div class="px-8 py-8">
                    <div class="text-center mb-8">
                        <h1 class="text-2xl font-bold text-gray-900">{{ __('auth.register_title') }}</h1>
                        <p class="mt-2 text-sm text-gray-600">{{ __('auth.register_subtitle') }}</p>
                    </div>

                    <form method="POST" action="{{ route('register') }}" class="space-y-6">
                        @csrf

                        <!-- Contact Person -->
                        <div>
                            <label for="contact_person" class="block text-sm font-medium text-gray-700 mb-1">
                                {{ __('auth.full_name') }}
                            </label>
                            <x-form.input type="text" name="contact_person" id="contact_person"
                                value="{{ old('contact_person') }}" placeholder="{{ __('auth.full_name_placeholder') }}" required autofocus
                                autocomplete="name" />
                            @error('contact_person')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Business Name -->
                        <div>
                            <label for="business_name" class="block text-sm font-medium text-gray-700 mb-1">
                                {{ __('auth.company_name') }}
                            </label>
                            <x-form.input type="text" name="business_name" id="business_name"
                                value="{{ old('business_name') }}" placeholder="{{ __('auth.company_name_placeholder') }}" required
                                autocomplete="organization" />
                            @error('business_name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <!-- Phone -->
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">
                                {{ __('auth.phone') }}
                            </label>
                            <x-form.input type="text" name="phone" id="phone" value="{{ old('phone') }}"
                                placeholder="{{ __('auth.phone_placeholder') }}" required autocomplete="tel" />
                            @error('phone')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Email -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
                                {{ __('auth.email') }}
                            </label>
                            <x-form.input type="email" name="email" id="email" value="{{ old('email') }}"
                                placeholder="{{ __('auth.email_placeholder') }}" required autocomplete="username" />
                            @error('email')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Password -->
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-1">
                                {{ __('auth.password') }}
                            </label>
                            <x-form.input type="password" name="password" id="password" placeholder="{{ __('auth.password_placeholder') }}"
                                required autocomplete="new-password" />
                            @error('password')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Confirm Password -->
                        <div>
                            <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-1">
                                {{ __('auth.confirm_password') }}
                            </label>
                            <x-form.input type="password" name="password_confirmation" id="password_confirmation"
                                placeholder="{{ __('auth.confirm_password_placeholder') }}" required autocomplete="new-password" />
                        </div>

                        <button type="submit"
                            class="w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-colors duration-200">
                            {{ __('auth.register_button') }}
                        </button>

                        @if ($errors->any())
                            <div class="mt-6 p-4 bg-red-50 rounded-md">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg"
                                            viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd"
                                                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                                clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-red-800">{{ __('auth.registration_failed') }}</h3>
                                        <div class="mt-2 text-sm text-red-700">
                                            <ul class="list-disc pl-5 space-y-1">
                                                @foreach ($errors->all() as $error)
                                                    <li>{{ $error }}</li>
                                                @endforeach
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </form>

                    <div class="mt-6">
                        <div class="relative">
                            <div class="absolute inset-0 flex items-center">
                                <div class="w-full border-t border-gray-300"></div>
                            </div>
                            <div class="relative flex justify-center text-sm">
                                <span class="px-2 bg-white text-gray-500">Đã có tài khoản?</span>
                            </div>
                        </div>

                        <div class="mt-6">
                            <a href="{{ route('login') }}"
                                class="w-full flex justify-center py-2.5 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-colors duration-200">
                                Đăng nhập
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
