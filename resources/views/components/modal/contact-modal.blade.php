<div 
    x-data="Object.assign(contactModal())"
    x-show="$store.modal.isOpen"
    x-init="
        $watch('$store.modal.isOpen', value => {
            if (value && formSubmitted) {
                resetForm();
            }
        });
    "
    class="fixed inset-0 z-50 flex items-center justify-center p-4"
    style="display: none;"
>
    <div 
        x-show="$store.modal.isOpen"
        x-transition:enter="ease-out duration-300"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="ease-in duration-200"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
        class="fixed inset-0 bg-black/60 backdrop-blur-sm"
    ></div>

    <!-- Modal Content -->
    <div 
        x-show="$store.modal.isOpen"
        x-transition:enter="ease-out duration-300"
        x-transition:enter-start="opacity-0 scale-95"
        x-transition:enter-end="opacity-100 scale-100"
        x-transition:leave="ease-in duration-200"
        x-transition:leave-start="opacity-100 scale-100"
        x-transition:leave-end="opacity-0 scale-95"
        class="relative z-10 w-full max-w-2xl bg-white rounded-2xl shadow-xl overflow-hidden"
    >
        <!-- Form section -->
        <div x-show="!formSubmitted">
            <!-- Header -->
            <div class="flex items-start justify-between p-5 border-b border-gray-200 rounded-t">
                <div>
                    <h3 class="text-xl font-bold text-gray-900" x-text="currentTitle()"></h3>
                    <p class="text-sm text-gray-500 mt-1">{{ __('messages.modal.contact_subtitle') }}</p>
                </div>
                <!-- Close button with popover confirm -->
                <div class="relative">
                    <button @click="showClosePopover = true" type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                    </button>
                    <!-- Popover confirm -->
                    <div x-show="showClosePopover" @click.away="showClosePopover = false" class="absolute right-0 mt-2 w-80 bg-white border border-gray-200 rounded-xl shadow-lg z-50 p-4" style="min-width:260px;">
                        <div class="text-gray-900 font-semibold mb-2">{{ __('messages.modal.close_confirm_title') }}</div>
                        <div class="text-gray-700 text-sm mb-4">{{ __('messages.modal.close_confirm_message') }}</div>
                        <div class="flex justify-end gap-2">
                            <button @click="$store.modal.close(); showClosePopover = false" class="px-3 py-1 bg-emerald-600 text-white rounded hover:bg-emerald-700 text-sm">Yes</button>
                            <button @click="showClosePopover = false" class="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 text-sm">No</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Progress bar -->
            <div class="p-5">
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-emerald-600 h-2 rounded-full transition-all duration-500" :style="`width: ${step === 1 ? '33' : (step === 2 ? '66' : '100')}%`"></div>
                </div>
            </div>

            <!-- Form steps -->
            <div class="p-6 pt-0 space-y-6">
                <!-- Step 1: Basic Info -->
                <div x-show.transition.in.opacity.duration.300="step === 1">
                    <div class="space-y-4">
                        <div>
                            <label for="name" class="block mb-2 text-sm font-medium text-gray-900">{{ __('messages.modal.name_label') }} <span class="text-red-500">*</span></label>
                            <x-form.input name="name" x-model="formData.name" placeholder="{{ __('messages.modal.name_placeholder') }}" required />
                        </div>
                        <div>
                            <label for="email" class="block mb-2 text-sm font-medium text-gray-900">{{ __('messages.modal.email_label') }} <span class="text-red-500">*</span></label>
                            <x-form.input name="email" x-model="formData.email" placeholder="{{ __('messages.modal.email_placeholder') }}" required type="email" />
                        </div>
                        <div>
                            <label for="phone" class="block mb-2 text-sm font-medium text-gray-900">{{ __('messages.forms.phone') }} <span class="text-red-500">*</span></label>
                            <x-form.input name="phone" x-model="formData.phone" placeholder="0989 123 456" required type="tel" />
                        </div>
                        <div>
                            <label for="company" class="block mb-2 text-sm font-medium text-gray-900">{{ __('messages.forms.company_name') }} <span class="text-red-500">*</span></label>
                            <x-form.input name="company" x-model="formData.company" placeholder="Cty TNHH CSlant/Cá nhân" required type="text" />
                        </div>
                    </div>
                </div>

                <!-- Step 2: Customer Needs -->
                <div x-show.transition.in.opacity.duration.300="step === 2">
                    <div class="space-y-6">
                        <div>
                            <h4 class="mb-3 text-sm font-medium text-gray-900">{{ __('messages.forms.website_type') }}</h4>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                @php
                                    $services = [
                                        'Website giới thiệu công ty/dịch vụ',
                                        'Website bán hàng (E-commerce)',
                                        'Landing page (Trang đích quảng cáo)',
                                        'Blog/Tạp chí online',
                                        'Website Portfolio (giới thiệu năng lực)',
                                        'Nâng cấp/Làm mới website cũ',
                                        'Yêu cầu khác (vui lòng mô tả thêm)'
                                    ];
                                @endphp
                                @foreach($services as $index => $service)
                                    <x-form.checkbox 
                                        x-model="formData.services" 
                                        value="{{ $service }}" 
                                        id="chk-services-{{ $index }}" 
                                        label="{{ $service }}" 
                                    />
                                @endforeach
                            </div>
                        </div>
                        <div>
                            <h4 class="mb-3 text-sm font-medium text-gray-900">{{ __('messages.forms.website_goal') }}</h4>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                @php
                                    $purposes = [
                                        'Tăng nhận diện thương hiệu',
                                        'Tìm kiếm khách hàng tiềm năng', 
                                        'Bán hàng trực tuyến',
                                        'Cung cấp thông tin',
                                        'Mục tiêu khác',
                                    ];
                                @endphp
                                @foreach($purposes as $index => $purpose)
                                    <x-form.radio 
                                        x-model="formData.purpose" 
                                        value="{{ $purpose }}" 
                                        id="chk-purposes-{{ $index }}" 
                                        label="{{ $purpose }}" 
                                    />
                                @endforeach
                            </div>
                        </div>
                        <div>
                            <h4 class="mb-3 text-sm font-medium text-gray-900">{{ __('messages.forms.requirements') }}</h4>
                            <div>
                                <x-form.textarea 
                                    name="details" 
                                    rows="4" 
                                    x-model="formData.details" 
                                    placeholder="Hãy chia sẻ thêm về lĩnh vực kinh doanh, website bạn thích, hoặc các tính năng bạn mong muốn..." 
                                    class="w-full"
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Details -->
                <div x-show.transition.in.opacity.duration.300="step === 3">
                    <div class="space-y-6">
                        <div>
                            <label for="details" class="block text-sm font-medium text-gray-900">{{ __('messages.forms.solution_package') }}</label>
                            <p class="text-gray-500 text-xs mb-2">{{ __('messages.forms.solution_package_help') }}</p>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                @php
                                    $packages = [
                                        'Express Web Basic',
                                        'Express Web Plus', 
                                        'Express Web Ultimate',
                                        'Tôi cần tư vấn thêm',
                                    ];
                                @endphp
                                @foreach($packages as $index => $package)
                                    <x-form.radio 
                                        x-model="formData.package" 
                                        value="{{ $package }}" 
                                        id="chk-package-{{ $index }}" 
                                        label="{{ $package }}" 
                                    />
                                @endforeach
                            </div>
                        </div>
                        <div>
                            <label for="details" class="block text-sm font-medium text-gray-900">{{ __('messages.forms.expected_budget') }}</label>
                            <p class="text-gray-500 text-xs mb-2">{{ __('messages.forms.budget_help') }}</p>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                @php
                                    $budgets = [
                                        'Dưới 5 triệu',
                                        '5 triệu - 15 triệu', 
                                        '15 triệu - 30 triệu',
                                        'Trên 30 triệu',
                                        'Tôi cần tư vấn thêm',
                                    ];
                                @endphp
                                @foreach($budgets as $index => $budget)
                                    <x-form.radio 
                                        x-model="formData.budget" 
                                        value="{{ $budget }}" 
                                        id="chk-budget-{{ $index }}" 
                                        label="{{ $budget }}" 
                                    />
                                @endforeach
                            </div>
                        </div>

                        <div>
                            <label for="details" class="block mb-2 text-sm font-medium text-gray-900">{{ __('messages.forms.reference_websites') }}</label>
                            <div>
                                <x-form.textarea 
                                    name="website_ref" 
                                    x-model="formData.website_ref" 
                                    placeholder="Hãy chia sẻ những website mà bạn tham khảo ưng ý..." 
                                    class="w-full"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer navigation -->
            <div class="flex items-center justify-between p-6 border-t border-gray-200 rounded-b">
                <x-ui.button @click="prevStep()" x-show="step > 1" variant="secondary" class="hover:cursor-pointer">Quay lại</x-ui.button>
                <div x-show="step === 1" class="flex-grow"></div>
                <x-ui.button @click="nextStep()" x-show="step < 3" class="hover:cursor-pointer">Tiếp theo</x-ui.button>
                <x-ui.button @click="submitForm()" x-show="step === 3" type="submit" class="hover:cursor-pointer">Gửi yêu cầu ngay</x-ui.button>
            </div>
        </div>

        <!-- Success message -->
        <div x-show="formSubmitted" class="p-8 text-center" style="display: none;">
            <div class="flex justify-center items-center w-16 h-16 mx-auto bg-emerald-100 rounded-full">
                <svg class="w-10 h-10 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mt-5">{{ __('messages.modal.success_title') }}</h3>
            <p class="text-gray-600 mt-2">{{ __('messages.modal.success_message') }}</p>
            <!-- Close button uses the store. -->
            <button @click="$store.modal.close()" class="mt-6 text-white bg-emerald-600 hover:bg-emerald-700 focus:ring-4 focus:outline-none focus:ring-emerald-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center">
                Đóng
            </button>
        </div>
    </div>
</div>
