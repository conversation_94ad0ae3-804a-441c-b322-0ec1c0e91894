@props([
    'position' => 'top', // top, bottom
    'container' => true
])

@php
    $containerClasses = $container ? 'container mx-auto px-4' : '';
    $positionClasses = $position === 'bottom' ? 'bottom-4' : 'top-4';
@endphp

<div class="fixed {{ $positionClasses }} left-0 right-0 z-50 {{ $containerClasses }}">
    <div class="max-w-md mx-auto space-y-2">
        
        <!-- Success Messages -->
        @if(session('success'))
            <x-alert type="success" :title="__('messages.common.success')">
                {{ session('success') }}
            </x-alert>
        @endif
        
        <!-- Error Messages -->
        @if(session('error'))
            <x-alert type="error" :title="__('messages.common.error')" :auto-hide="false">
                {{ session('error') }}
            </x-alert>
        @endif
        
        <!-- Warning Messages -->
        @if(session('warning'))
            <x-alert type="warning" :title="__('messages.common.warning')">
                {{ session('warning') }}
            </x-alert>
        @endif
        
        <!-- Info Messages -->
        @if(session('info'))
            <x-alert type="info" :title="__('messages.common.info')">
                {{ session('info') }}
            </x-alert>
        @endif
        
        <!-- Status Messages (generic info) -->
        @if(session('status'))
            <x-alert type="info">
                {{ session('status') }}
            </x-alert>
        @endif
        
        <!-- Validation Errors -->
        @if($errors->any())
            <x-alert type="error" :title="__('messages.validation.errors')" :auto-hide="false">
                <ul class="list-disc list-inside space-y-1">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </x-alert>
        @endif
        
        <!-- Custom slot content -->
        {{ $slot }}
    </div>
</div>
