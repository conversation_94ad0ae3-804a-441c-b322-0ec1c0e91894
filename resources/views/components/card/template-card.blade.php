<div class="swiper-slide bg-white rounded-lg shadow-lg overflow-hidden group relative">
    <div class="absolute top-4 right-4 flex flex-col gap-1 items-end z-10">
        @if(!empty($template->is_featured))
            <div class="px-3 py-1 bg-emerald-500 text-white text-xs font-medium rounded-full">{{ __('messages.website.featured') }}</div>
        @endif
        @if(!empty($template->is_new))
            <div class="px-3 py-1 bg-blue-500 text-white text-xs font-medium rounded-full">{{ __('messages.website.new') }}</div>
        @endif
    </div>
    <div class="relative">
        <a href="{{ route('templates.show', $template->slug) }}">
            @if ($template->featured_image_url)
                <img src="{{ $template->featured_image_url }}" alt="{{ $template->name }}" class="w-full h-48 object-cover">
            @else
                <div class="w-full h-48 bg-gray-100 flex items-center justify-center text-gray-400">
                    <i class="fa-regular fa-image text-2xl"></i>
                </div>
            @endif
        </a>
        <div class="absolute bottom-2 left-2 flex flex-wrap gap-1">
            @if($template->category)
            <span class="bg-white bg-opacity-90 text-gray-800 text-xs px-2 py-1 rounded">{{ $template->category->name }}</span>
            @endif
            <span class="bg-white bg-opacity-90 text-gray-800 text-xs px-2 py-1 rounded">Responsive</span>
        </div>
    </div>
    <div class="p-6">
        <h3 class="font-bold text-lg text-gray-800 mb-2 group-hover:text-emerald-600 transition-colors duration-300 overflow-hidden text-ellipsis line-clamp-2 min-h-[3rem] leading-snug">
            <a href="{{ route('templates.show', $template->slug) }}" class="hover:underline">
                {{ $template->name }}
            </a>
        </h3>
        <p class="text-gray-500 text-sm mb-4 line-clamp-2 overflow-hidden text-ellipsis min-h-[2.5rem] leading-snug" title="{{ $template->description ?? 'Mẫu website chuyên nghiệp với thiết kế hiện đại, đầy đủ tính năng cần thiết.' }}">
            {{ $template->description ?? 'Mẫu website chuyên nghiệp với thiết kế hiện đại, đầy đủ tính năng cần thiết.' }}
        </p>
        <div class="flex items-center justify-between">
            <div class="text-emerald-600 font-bold">
                {{ $template->price }}
                @if($template->sale_price)
                <span class="text-gray-400 text-sm font-normal line-through ml-1">{{ $template->formatted_original_price }}</span>
                @endif
            </div>
            <div class="flex items-center text-yellow-400 text-sm">

            </div>
        </div>
        <div class="mt-4 flex justify-between items-center">
            <x-ui.button href="{{ route('templates.show', $template->slug) }}">Chọn mẫu</x-ui.button>
            <x-ui.button href="{{ route('demo.show', $template->slug) }}" variant="link">Xem Demo</x-ui.button>
        </div>
    </div>
</div>
