@props([
    'type' => 'info', // success, error, warning, info
    'title' => null,
    'dismissible' => true,
    'autoHide' => true,
    'duration' => 5000,
    'showProgress' => true,
    'icon' => true
])

@php
    $alertClasses = match($type) {
        'success' => 'bg-green-50 border-green-200 text-green-800',
        'error' => 'bg-red-50 border-red-200 text-red-800',
        'warning' => 'bg-yellow-50 border-yellow-200 text-yellow-800',
        'info' => 'bg-blue-50 border-blue-200 text-blue-800',
        default => 'bg-gray-50 border-gray-200 text-gray-800'
    };
    
    $iconClasses = match($type) {
        'success' => 'text-green-500',
        'error' => 'text-red-500',
        'warning' => 'text-yellow-500',
        'info' => 'text-blue-500',
        default => 'text-gray-500'
    };
    
    $progressClasses = match($type) {
        'success' => 'bg-green-500',
        'error' => 'bg-red-500',
        'warning' => 'bg-yellow-500',
        'info' => 'bg-blue-500',
        default => 'bg-gray-500'
    };
@endphp

<div x-data="alertComponent({{ $autoHide ? 'true' : 'false' }}, {{ $duration }})" 
     x-show="show" 
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0 transform translate-y-2"
     x-transition:enter-end="opacity-100 transform translate-y-0"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100 transform translate-y-0"
     x-transition:leave-end="opacity-0 transform translate-y-2"
     class="relative mb-4 p-4 border rounded-lg {{ $alertClasses }} shadow-sm"
     role="alert">
     
    @if($showProgress && $autoHide)
        <!-- Progress Bar -->
        <div class="absolute top-0 left-0 h-1 {{ $progressClasses }} rounded-t-lg transition-all duration-100 ease-linear"
             :style="`width: ${progress}%`"></div>
    @endif
    
    <div class="flex items-start">
        @if($icon)
            <!-- Icon -->
            <div class="flex-shrink-0 mr-3">
                @if($type === 'success')
                    <svg class="w-5 h-5 {{ $iconClasses }}" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                @elseif($type === 'error')
                    <svg class="w-5 h-5 {{ $iconClasses }}" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                    </svg>
                @elseif($type === 'warning')
                    <svg class="w-5 h-5 {{ $iconClasses }}" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                @else
                    <svg class="w-5 h-5 {{ $iconClasses }}" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                    </svg>
                @endif
            </div>
        @endif
        
        <!-- Content -->
        <div class="flex-1 min-w-0">
            @if($title)
                <h3 class="text-sm font-semibold mb-1">{{ $title }}</h3>
            @endif
            <div class="text-sm">
                {{ $slot }}
            </div>
        </div>
        
        @if($dismissible)
            <!-- Close Button -->
            <div class="flex-shrink-0 ml-3">
                <button @click="hide()" 
                        class="inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 hover:bg-black hover:bg-opacity-10 transition-colors"
                        :class="{
                            'focus:ring-green-600': '{{ $type }}' === 'success',
                            'focus:ring-red-600': '{{ $type }}' === 'error',
                            'focus:ring-yellow-600': '{{ $type }}' === 'warning',
                            'focus:ring-blue-600': '{{ $type }}' === 'info'
                        }">
                    <span class="sr-only">{{ __('messages.common.close') }}</span>
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>
                </button>
            </div>
        @endif
    </div>
</div>

@once
    @push('scripts')
    <script>
        function alertComponent(autoHide = true, duration = 5000) {
            return {
                show: true,
                progress: 100,
                interval: null,
                
                init() {
                    if (autoHide) {
                        this.startCountdown();
                    }
                },
                
                startCountdown() {
                    const step = 100 / (duration / 100);
                    this.interval = setInterval(() => {
                        this.progress -= step;
                        if (this.progress <= 0) {
                            this.hide();
                        }
                    }, 100);
                },
                
                hide() {
                    if (this.interval) {
                        clearInterval(this.interval);
                    }
                    this.show = false;
                },
                
                pause() {
                    if (this.interval) {
                        clearInterval(this.interval);
                    }
                },
                
                resume() {
                    if (this.show && this.progress > 0) {
                        this.startCountdown();
                    }
                }
            }
        }
    </script>
    @endpush
@endonce
