@props([
    'template',
    'size' => 'md',
    'position' => 'absolute',
    'showTooltip' => true,
    'variant' => 'default'
])

<div class="{{ $containerClasses }}">
    <button
        data-favorite-button
        class="{{ $buttonClasses }}"
        @if($isLoggedIn)
            data-template-id="{{ $template->id }}"
            data-is-favorited="{{ $isFavorited ? 'true' : 'false' }}"
        @endif
        data-is-logged-in="{{ $isLoggedIn ? 'true' : 'false' }}"
        aria-label="{{ $tooltipText }}"
        @if($showTooltip)
            title="{{ $tooltipText }}"
        @endif
    >
        <!-- Heart Icon -->
        <svg
            class="{{ $iconClasses }} heart-icon transition-all duration-400 text-pink-500 {{ $isFavorited ? ' fill-current' : 'group-hover/fav:text-pink-500' }} hover:scale-110"
            fill="{{ $isFavorited ? 'currentColor' : 'none' }}"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
            ></path>
        </svg>

        <!-- Loading Spinner (hidden by default) -->
        <svg
            class="loading-spinner {{ $iconClasses }} animate-spin hidden transition-all duration-200"
            viewBox="0 0 24 24"
            fill="none"
        >
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>

        @if($showTooltip)
            <!-- Tooltip -->
            <div class="tooltip absolute -top-12 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded opacity-0 group-hover/fav:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                <span class="tooltip-text">{{ $tooltipText }}</span>
                <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-900"></div>
            </div>
        @endif
    </button>
</div>

@once
    @push('scripts')
    <script>
        (function() {
            'use strict';

            // Constants
            const TOAST_TYPES = {
                SUCCESS: 'success',
                ERROR: 'error',
                INFO: 'info'
            };

            // Cache DOM elements and state
            const favoriteButtons = {
                /**
                 * Initialize the favorite buttons
                 */
                init() {
                    document.addEventListener('click', this.handleClick.bind(this));
                },

                /**
                 * Handle click events on favorite buttons
                 * @param {Event} event - The click event
                 */
                handleClick(event) {
                    const button = event.target.closest('[data-favorite-button]');
                    if (!button) return;

                    event.preventDefault();
                    if (button.hasAttribute('disabled')) return;

                    const templateId = button.dataset.templateId;
                    const isLoggedIn = button.dataset.isLoggedIn === 'true';

                    if (!isLoggedIn) return this.showLoginPrompt();

                    this.toggleFavorite(templateId, button).catch(console.error);
                },

                /**
                 * Toggle favorite status for a template
                 * @param {string} templateId - The ID of the template
                 * @param {HTMLElement} button - The button element
                 */
                async toggleFavorite(templateId, button) {
                    const elements = this.getButtonElements(button);
                    if (!elements) return;

                    const { heartIcon, loadingSpinner } = elements;

                    // Update UI immediately for better UX
                    this.toggleLoadingState(button, heartIcon, loadingSpinner, true);

                    try {
                        const response = await this.sendFavoriteRequest(templateId);

                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }

                        const data = await response.json();

                        if (data.success) {
                            this.updateButtonState(button, elements, data.is_favorited);
                            this.showNotification(data.message, TOAST_TYPES.SUCCESS);
                        } else {
                            throw new Error(data.message || '{{ __("messages.common.error_occurred") }}');
                        }
                    } catch (error) {
                        console.error('Favorite error:', error);
                        this.showNotification(
                            error.message || '{{ __("messages.common.error_occurred") }}',
                            TOAST_TYPES.ERROR
                        );
                    } finally {
                        this.toggleLoadingState(button, heartIcon, loadingSpinner, false);
                    }
                },

                /**
                 * Get all button elements
                 * @param {HTMLElement} button - The button element
                 * @returns {Object|null} Object containing button elements or null if not found
                 */
                getButtonElements(button) {
                    const heartIcon = button.querySelector('.heart-icon');
                    const loadingSpinner = button.querySelector('.loading-spinner');
                    const tooltipText = button.querySelector('.tooltip-text');

                    if (!heartIcon || !loadingSpinner) {
                        console.error('Required button elements not found');
                        return null;
                    }

                    return { heartIcon, loadingSpinner, tooltipText };
                },

                /**
                 * Send favorite toggle request to the server
                 * @param {string} templateId - The ID of the template
                 * @returns {Promise<Response>} The fetch response
                 */
                sendFavoriteRequest(templateId) {
                    return fetch(`/dashboard/template-favorites/${templateId}/toggle`, {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': `{{ csrf_token() }}`,
                            // 'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json',
                            'Content-Type': 'application/json'
                        },
                        credentials: 'same-origin'
                    });
                },

                /**
                 * Toggle loading state of the button
                 * @param {HTMLElement} button - The button element
                 * @param {HTMLElement} heartIcon - The heart icon element
                 * @param {HTMLElement} loadingSpinner - The loading spinner element
                 * @param {boolean} isLoading - Whether the button is in loading state
                 */
                toggleLoadingState(button, heartIcon, loadingSpinner, isLoading) {
                    button.disabled = isLoading;
                    heartIcon.classList.toggle('hidden', isLoading);
                    loadingSpinner.classList.toggle('hidden', !isLoading);
                },

                /**
                 * Update the button state after toggle
                 * @param {HTMLElement} button - The button element
                 * @param {Object} elements - Button elements
                 * @param {boolean} isFavorited - Whether the template is favorited
                 */
                updateButtonState(button, { heartIcon, tooltipText }, isFavorited) {
                    // Update data attribute
                    button.dataset.isFavorited = isFavorited ? 'true' : 'false';

                    // Update heart icon
                    heartIcon.classList.toggle('text-gray-400', !isFavorited);
                    heartIcon.classList.toggle('text-pink-500', isFavorited);
                    heartIcon.classList.toggle('fill-current', isFavorited);
                    heartIcon.setAttribute('fill', isFavorited ? 'currentColor' : 'none');

                    // Update tooltip text if exists
                    if (tooltipText) {
                        tooltipText.textContent = isFavorited
                            ? '{{ __("messages.template.remove_from_favorites") }}'
                            : '{{ __("messages.template.add_to_favorites") }}';
                    }

                    // Add animation if favorited
                    if (isFavorited) {
                        this.animateHeart(heartIcon);
                    }
                },

                /**
                 * Animate the heart icon
                 * @param {HTMLElement} heartIcon - The heart icon element
                 */
                animateHeart(heartIcon) {
                    heartIcon.classList.add('animate-bounce');
                    const animationTimeout = setTimeout(() => {
                        heartIcon.classList.remove('animate-bounce');
                        clearTimeout(animationTimeout);
                    }, 1500);
                },

                /**
                 * Show a notification to the user
                 * @param {string} message - The message to show
                 * @param {string} type - The type of notification (success, error, info)
                 */
                showNotification(message, type = 'info') {
                    // Use the global showMiniToast function
                    if (typeof window.showMiniToast === 'function') {
                        window.showMiniToast(message, type);
                    }
                },

                /**
                 * Show login prompt for non-authenticated users
                 */
                showLoginPrompt() {
                    if (typeof window.showMiniToast === 'function') {
                        window.showMiniToast('{{ __("messages.auth.login_to_favorite") }}', 'info');
                        // Redirect after a short delay to allow toast to be seen
                        setTimeout(() => {
                            window.location.href = '{{ route('login') }}?redirect={{ request()->fullUrl() }}';
                        }, 1500);
                    } else {
                        window.location.href = '{{ route('login') }}?redirect={{ request()->fullUrl() }}';
                    }
                }
            };

            // Initialize when DOM is loaded
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => favoriteButtons.init());
            } else {
                favoriteButtons.init();
            }
        })();
    </script>
    @endpush
@endonce
