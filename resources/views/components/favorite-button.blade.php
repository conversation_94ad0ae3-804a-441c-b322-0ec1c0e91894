@props([
    'template',
    'size' => 'md',
    'position' => 'absolute',
    'showTooltip' => true,
    'variant' => 'default'
])

@php
    $user = auth('customer_user')->user();
    $isFavorited = $user ? $template->isFavoritedBy($user) : false;
    
    $sizeClasses = match($size) {
        'sm' => 'w-4 h-4',
        'md' => 'w-5 h-5',
        'lg' => 'w-6 h-6',
        'xl' => 'w-8 h-8',
        default => 'w-5 h-5'
    };
    
    $buttonSizeClasses = match($size) {
        'sm' => 'p-1.5',
        'md' => 'p-2',
        'lg' => 'p-2.5',
        'xl' => 'p-3',
        default => 'p-2'
    };
    
    $positionClasses = match($position) {
        'absolute' => 'absolute top-4 left-4 z-10',
        'relative' => 'relative',
        'inline' => 'inline-block',
        default => 'absolute top-4 left-4 z-10'
    };
    
    $variantClasses = match($variant) {
        'default' => 'bg-white/90 backdrop-blur-sm shadow-md hover:bg-white',
        'transparent' => 'bg-transparent hover:bg-white/10',
        'solid' => 'bg-white shadow-lg hover:shadow-xl',
        'minimal' => 'hover:bg-gray-100',
        default => 'bg-white/90 backdrop-blur-sm shadow-md hover:bg-white'
    };
@endphp

<div class="{{ $positionClasses }}">
    @auth('customer_user')
        <button 
            onclick="toggleTemplateFavorite({{ $template->id }}, this)"
            class="favorite-btn {{ $buttonSizeClasses }} {{ $variantClasses }} rounded-full transition-all duration-200 group/fav focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 hover:scale-105"
            data-template-id="{{ $template->id }}"
            data-is-favorited="{{ $isFavorited ? 'true' : 'false' }}"
            aria-label="{{ $isFavorited ? __('messages.template.remove_from_favorites') : __('messages.template.add_to_favorites') }}"
            @if($showTooltip) title="{{ $isFavorited ? __('messages.template.remove_from_favorites') : __('messages.template.add_to_favorites') }}" @endif
        >
            <!-- Heart Icon -->
            <svg class="heart-icon {{ $sizeClasses }} transition-all duration-200 {{ $isFavorited ? 'text-red-500 fill-current' : 'text-gray-400 group-hover/fav:text-red-500' }}"
                 viewBox="0 0 24 24" 
                 stroke="currentColor" 
                 stroke-width="2"
                 fill="{{ $isFavorited ? 'currentColor' : 'none' }}">
                <path stroke-linecap="round" 
                      stroke-linejoin="round"
                      d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
            
            <!-- Loading Spinner (hidden by default) -->
            <svg class="loading-spinner {{ $sizeClasses }} animate-spin text-gray-400 hidden absolute inset-0 m-auto"
                 viewBox="0 0 24 24" 
                 fill="none">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            
            @if($showTooltip)
                <!-- Tooltip -->
                <div class="tooltip absolute -top-12 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded opacity-0 group-hover/fav:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                    <span class="tooltip-text">{{ $isFavorited ? __('messages.template.remove_from_favorites') : __('messages.template.add_to_favorites') }}</span>
                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-900"></div>
                </div>
            @endif
        </button>
    @else
        <button 
            onclick="showLoginPrompt()"
            class="favorite-btn {{ $buttonSizeClasses }} {{ $variantClasses }} rounded-full transition-all duration-200 group/fav focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-offset-2 hover:scale-105"
            aria-label="{{ __('messages.auth.login_to_favorite') }}"
            @if($showTooltip) title="{{ __('messages.auth.login_to_favorite') }}" @endif
        >
            <!-- Heart Icon -->
            <svg class="heart-icon {{ $sizeClasses }} transition-all duration-200 text-gray-400 group-hover/fav:text-red-400"
                 viewBox="0 0 24 24" 
                 stroke="currentColor" 
                 stroke-width="2"
                 fill="none">
                <path stroke-linecap="round" 
                      stroke-linejoin="round"
                      d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
            
            @if($showTooltip)
                <!-- Tooltip -->
                <div class="tooltip absolute -top-12 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded opacity-0 group-hover/fav:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                    <span class="tooltip-text">{{ __('messages.auth.login_to_favorite') }}</span>
                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-900"></div>
                </div>
            @endif
        </button>
    @endauth
</div>

@once
    @push('scripts')
    <script>
        // Global favorite functionality
        window.toggleTemplateFavorite = function(templateId, buttonElement) {
            const heartIcon = buttonElement.querySelector('.heart-icon');
            const loadingSpinner = buttonElement.querySelector('.loading-spinner');
            const tooltipText = buttonElement.querySelector('.tooltip-text');
            const isCurrentlyFavorited = buttonElement.dataset.isFavorited === 'true';
            
            // Show loading state
            buttonElement.disabled = true;
            heartIcon.classList.add('hidden');
            loadingSpinner.classList.remove('hidden');
            
            fetch(`/template-favorites/${templateId}/toggle`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update button state
                    buttonElement.dataset.isFavorited = data.is_favorited ? 'true' : 'false';
                    
                    if (data.is_favorited) {
                        // Added to favorites
                        heartIcon.classList.remove('text-gray-400');
                        heartIcon.classList.add('text-red-500', 'fill-current');
                        heartIcon.setAttribute('fill', 'currentColor');
                        
                        if (tooltipText) {
                            tooltipText.textContent = '{{ __('messages.template.remove_from_favorites') }}';
                        }
                        
                        // Heart beat animation
                        heartIcon.classList.add('animate-bounce');
                        setTimeout(() => {
                            heartIcon.classList.remove('animate-bounce');
                        }, 600);
                        
                    } else {
                        // Removed from favorites
                        heartIcon.classList.remove('text-red-500', 'fill-current');
                        heartIcon.classList.add('text-gray-400');
                        heartIcon.setAttribute('fill', 'none');
                        
                        if (tooltipText) {
                            tooltipText.textContent = '{{ __('messages.template.add_to_favorites') }}';
                        }
                    }
                    
                    // Show success message
                    if (typeof showToast === 'function') {
                        showToast(data.message, 'success');
                    } else if (typeof showMiniToast === 'function') {
                        showMiniToast(data.message, 'success');
                    }
                    
                } else {
                    const errorMsg = data.message || '{{ __('messages.common.error_occurred') }}';
                    if (typeof showToast === 'function') {
                        showToast(errorMsg, 'error');
                    } else if (typeof showMiniToast === 'function') {
                        showMiniToast(errorMsg, 'error');
                    } else {
                        alert(errorMsg);
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                const errorMsg = '{{ __('messages.common.error_occurred') }}';
                if (typeof showToast === 'function') {
                    showToast(errorMsg, 'error');
                } else if (typeof showMiniToast === 'function') {
                    showMiniToast(errorMsg, 'error');
                } else {
                    alert(errorMsg);
                }
            })
            .finally(() => {
                // Hide loading state
                buttonElement.disabled = false;
                heartIcon.classList.remove('hidden');
                loadingSpinner.classList.add('hidden');
            });
        };
        
        // Show login prompt for non-authenticated users
        window.showLoginPrompt = function() {
            if (confirm('{{ __('messages.auth.login_required_message') }}')) {
                window.location.href = '{{ route('login') }}';
            }
        };
    </script>
    @endpush
@endonce
