@extends('layouts.default')

@push('styles')
    @vite(['resources/css/pages/templates/show.css'])
@endpush

@section('content')
    <div class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Breadcrumb -->
            @php
                $breadcrums = [
                    [
                        'text' => 'Trang chủ',
                        'url' => route('home')
                    ],
                    [
                        'text' => 'Mẫu website',
                        'url' => route('templates.index')
                    ],
                    [
                        'text' => $template->category->name,
                        'url' => route('templates.category', $template->category->slug)
                    ],
                    [
                        'text' => $template->name,
                    ],
                ];
            @endphp
            <div class="mb-6">
                <x-ui.breadcrumb :items="$breadcrums" />
            </div>

            <!-- Main Content -->
            <div class="bg-white">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 relative">
                    <!-- Left Column - Template Preview -->
                    <div class="md:col-span-2">
                        <!-- Screenshot Slider -->
                        <div class="swiper template-swiper rounded-lg overflow-hidden mb-4 bg-gray-100 relative">
                            <div class="swiper-wrapper">
                                @forelse($template->screenshot_urls as $screenshotUrl)
                                    <div class="swiper-slide">
                                        <img src="{{ $screenshotUrl }}" alt="{{ $template->name }} - Screenshot {{ $loop->iteration }}"
                                            class="w-full h-auto object-cover">
                                    </div>
                                @empty
                                    @if ($template->featured_image_url)
                                        <div class="swiper-slide">
                                            <img src="{{ $template->featured_image_url }}" alt="{{ $template->name }}"
                                                class="w-full h-auto object-cover">
                                        </div>
                                    @else
                                        <div class="w-full h-full bg-gray-100 flex items-center justify-center text-gray-400">
                                            <i class="fa-regular fa-image text-2xl"></i>
                                        </div>
                                    @endif
                                @endforelse
                            </div>
                            <!-- Add Pagination -->
                            <div class="swiper-pagination"></div>
                            <!-- Navigation Buttons -->
                            <div class="swiper-button-prev"></div>
                            <div class="swiper-button-next"></div>
                            <!-- Slide Counter -->
                            <div class="absolute bottom-3 right-3 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded-md z-10">
                                <span class="current-slide">1</span>/<span class="total-slides">{{ $template->screenshots ? count($template->screenshots) : 1 }}</span>
                            </div>
                        </div>

                        <!-- Thumbnail Slider -->
                        @if($template->screenshots && count($template->screenshots) > 1)
                        <div class="swiper thumbnail-swiper mt-2">
                            <div class="swiper-wrapper">
                                @foreach($template->screenshots as $screenshot)
                                    <div class="swiper-slide cursor-pointer opacity-50 hover:opacity-100 transition-opacity duration-200 rounded overflow-hidden" style="padding: 2px;">
                                        <img src="{{ $screenshot }}" alt="Thumbnail {{ $loop->iteration }}"
                                            class="w-full h-16 md:h-20 object-cover rounded">
                                    </div>
                                @endforeach
                            </div>
                        </div>
                        @endif

                        <!-- CSlant Commitments -->
                        <div class="mt-6 p-4 bg-white rounded-lg shadow-sm border border-gray-100">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">CSlant cam kết</h3>
                            <div class="grid grid-cols-2 gap-3">
                                <!-- Commitment 1 -->
                                <div class="flex items-center gap-3 p-2 rounded bg-emerald-50 hover:bg-emerald-100 transition-colors">
                                    <div class="flex-shrink-0">
                                        <div class="h-8 w-8 rounded-full bg-emerald-100 flex items-center justify-center">
                                            <svg class="h-4 w-4 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944 11.955 11.955 0 01.382 8.984M5.618 15.016A11.955 11.955 0 0112 21.056 11.955 11.955 0 0123.618 15.016M12 12V2.944" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="min-w-0">
                                        <h4 class="text-sm font-medium text-gray-900 truncate">Chất lượng</h4>
                                        <p class="text-xs text-gray-500 truncate">Ổn định & bảo mật</p>
                                    </div>
                                </div>
                                <!-- Commitment 2 -->
                                <div class="flex items-center gap-3 p-2 rounded bg-emerald-50 hover:bg-emerald-100 transition-colors">
                                    <div class="flex-shrink-0">
                                        <div class="h-8 w-8 rounded-full bg-emerald-100 flex items-center justify-center">
                                            <svg class="h-4 w-4 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 8c1.657 0 3 .895 3 2s-1.343 2-3 2-3-.895-3-2 1.343-2 3-2zM9 12a3 3 0 100-6 3 3 0 000 6z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M17.657 16.657A8 8 0 016.343 6.343L12 12l-5.657 5.657a8 8 0 0111.314 0z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="min-w-0">
                                        <h4 class="text-sm font-medium text-gray-900 truncate">Hỗ trợ 24/7</h4>
                                        <p class="text-xs text-gray-500 truncate">Đội ngũ chuyên gia</p>
                                    </div>
                                </div>
                                <!-- Commitment 3 -->
                                <div class="flex items-center gap-3 p-2 rounded bg-emerald-50 hover:bg-emerald-100 transition-colors">
                                    <div class="flex-shrink-0">
                                        <div class="h-8 w-8 rounded-full bg-emerald-100 flex items-center justify-center">
                                            <svg class="h-4 w-4 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="min-w-0">
                                        <h4 class="text-sm font-medium text-gray-900 truncate">Giá cả</h4>
                                        <p class="text-xs text-gray-500 truncate">Tối ưu chi phí</p>
                                    </div>
                                </div>
                                <!-- Commitment 4 -->
                                <div class="flex items-center gap-3 p-2 rounded bg-emerald-50 hover:bg-emerald-100 transition-colors">
                                    <div class="flex-shrink-0">
                                        <div class="h-8 w-8 rounded-full bg-emerald-100 flex items-center justify-center">
                                            <svg class="h-4 w-4 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm-9-9a9 9 0 019-9" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="min-w-0">
                                        <h4 class="text-sm font-medium text-gray-900 truncate">Thiết kế</h4>
                                        <p class="text-xs text-gray-500 truncate">Độc đáo & khác biệt</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pricing & Features -->
                        <div class="mt-8" id="pricing-table">
                            <h3 class="text-2xl font-bold text-gray-900 mb-6 text-center">Bảng so sánh các gói của dịch vụ Express Web</h3>

                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tính năng</th>
                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">GÓI BASIC</th>
                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">GÓI PLUS</th>
                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">GÓI ULTIMATE</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Đối tượng phù hợp</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">Cá nhân, Blog</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">Doanh nghiệp nhỏ</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">Bán hàng trực tuyến</td>
                                        </tr>
                                        <tr class="bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Giao diện (UI)</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">Cơ bản, 1 mẫu</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">Chuyên nghiệp, 3 mẫu</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">Cao cấp, 5 mẫu</td>
                                        </tr>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Số lượng trang</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">Tối đa 5 trang</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">Tối đa 15 trang</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">Không giới hạn</td>
                                        </tr>
                                        <tr class="bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Nền tảng</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">WordPress</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">WordPress/HTML5</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">WooCommerce</td>
                                        </tr>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Tính năng tùy chỉnh</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">Hạn chế</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">Nhiều lựa chọn</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">Đầy đủ</td>
                                        </tr>
                                        <tr class="bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Tối ưu SEO Cơ bản</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">✓</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">✓</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">✓</td>
                                        </tr>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Thiết bị di động</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">Responsive cơ bản</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">Responsive đầy đủ</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">Tối ưu Mobile-first</td>
                                        </tr>
                                        <tr class="bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Thời gian triển khai</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">3-5 ngày</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">7-10 ngày</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">10-15 ngày</td>
                                        </tr>
                                        <tr class="border-t-2 border-gray-300">
                                            <td class="px-6 py-4 whitespace-nowrap text-lg font-bold text-gray-900">Chi phí</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                                <span class="text-lg font-semibold text-emerald-600">1.500.000₫</span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                                <span class="text-lg font-semibold text-emerald-600">3.000.000₫</span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                                <span class="text-lg font-semibold text-emerald-600">5.000.000₫</span>
                                            </td>
                                        </tr>
                                        <tr class="bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap font-medium text-gray-900">
                                                Đăng ký dùng thử
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                                <x-ui.button class="px-10 hover:cursor-pointer" x-data @click="$store.trialModal.open({{ $template->id }}, '{{ $template->name }}')">Chọn gói</x-ui.button>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                                <x-ui.button class="px-10 hover:cursor-pointer" x-data @click="$store.trialModal.open({{ $template->id }}, '{{ $template->name }}')">Chọn gói</x-ui.button>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                                <x-ui.button class="px-10 hover:cursor-pointer" x-data @click="$store.trialModal.open({{ $template->id }}, '{{ $template->name }}')">Chọn gói</x-ui.button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Additional Notes -->
                            <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                                <h4 class="font-medium text-blue-800 mb-2">Lưu ý:</h4>
                                <ul class="list-disc pl-5 text-sm text-blue-700 space-y-1">
                                    <li>Giá trên chưa bao gồm VAT</li>
                                    <li>Hỗ trợ kỹ thuật 24/7 cho tất cả các gói</li>
                                    <li>Bảo hành trọn đời đối với giao diện</li>
                                    <li>Miễn phí tên miền quốc tế năm đầu tiên</li>
                                </ul>
                            </div>
                        </div>

                        <!-- FAQ Section -->
                        <div class="mt-16">
                            <h2 class="text-2xl font-bold text-gray-900 mb-8 text-center">Câu hỏi thường gặp</h2>
                            <div class="space-y-4 max-w-3xl mx-auto">
                                <!-- FAQ Item 1 -->
                                <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                                    <button class="faq-question w-full px-6 py-4 text-left flex justify-between items-center focus:outline-none">
                                        <span class="font-medium text-gray-900">Mất bao lâu để hoàn thiện website?</span>
                                        <svg class="h-5 w-5 text-gray-500 transform transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </button>
                                    <div class="faq-answer px-6 pb-4 hidden">
                                        <p class="text-gray-600">Thời gian hoàn thiện website phụ thuộc vào gói dịch vụ bạn chọn. Thông thường, website sẽ được bàn giao trong vòng 7-14 ngày làm việc kể từ khi nhận đủ thông tin và nội dung từ phía khách hàng.</p>
                                    </div>
                                </div>

                                <!-- FAQ Item 2 -->
                                <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                                    <button class="faq-question w-full px-6 py-4 text-left flex justify-between items-center focus:outline-none">
                                        <span class="font-medium text-gray-900">Tôi có thể tùy chỉnh giao diện sau khi bàn giao không?</span>
                                        <svg class="h-5 w-5 text-gray-500 transform transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </button>
                                    <div class="faq-answer px-6 pb-4 hidden">
                                        <p class="text-gray-600">Có, bạn hoàn toàn có thể tùy chỉnh giao diện sau khi bàn giao. Chúng tôi sẽ cung cấp cho bạn quyền quản trị để chỉnh sửa nội dung, hình ảnh và một số thành phần giao diện. Nếu cần thay đổi lớn về giao diện, bạn có thể liên hệ đội ngũ kỹ thuật để được hỗ trợ.</p>
                                    </div>
                                </div>

                                <!-- FAQ Item 3 -->
                                <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                                    <button class="faq-question w-full px-6 py-4 text-left flex justify-between items-center focus:outline-none">
                                        <span class="font-medium text-gray-900">Chi phí duy trì hàng năm là bao nhiêu?</span>
                                        <svg class="h-5 w-5 text-gray-500 transform transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </button>
                                    <div class="faq-answer px-6 pb-4 hidden">
                                        <p class="text-gray-600">Chi phí duy trì hàng năm bao gồm phí tên miền (khoảng 300,000đ/năm) và phí hosting (từ 1,500,000đ/năm tùy theo gói). Nếu bạn đăng ký gói dịch vụ trọn gói của chúng tôi, bạn sẽ được miễn phí hosting năm đầu tiên.</p>
                                    </div>
                                </div>

                                <!-- FAQ Item 4 -->
                                <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                                    <button class="faq-question w-full px-6 py-4 text-left flex justify-between items-center focus:outline-none">
                                        <span class="font-medium text-gray-900">Tôi có được hỗ trợ sau khi bàn giao không?</span>
                                        <svg class="h-5 w-5 text-gray-500 transform transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </button>
                                    <div class="faq-answer px-6 pb-4 hidden">
                                        <p class="text-gray-600">Đúng vậy, chúng tôi cung cấp chính sách bảo hành 3 tháng miễn phí sau khi bàn giao. Trong thời gian này, mọi vấn đề phát sinh liên quan đến lỗi kỹ thuật sẽ được hỗ trợ miễn phí. Ngoài ra, bạn có thể đăng ký gói hỗ trợ kỹ thuật dài hạn với mức phí ưu đãi.</p>
                                    </div>
                                </div>

                                <!-- FAQ Item 5 -->
                                <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                                    <button class="faq-question w-full px-6 py-4 text-left flex justify-between items-center focus:outline-none">
                                        <span class="font-medium text-gray-900">Tôi có thể nâng cấp gói dịch vụ sau này không?</span>
                                        <svg class="h-5 w-5 text-gray-500 transform transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </button>
                                    <div class="faq-answer px-6 pb-4 hidden">
                                        <p class="text-gray-600">Hoàn toàn có thể. Bạn có thể nâng cấp lên gói dịch vụ cao cấp hơn bất cứ lúc nào. Chúng tôi sẽ điều chỉnh chi phí theo tỷ lệ thời gian sử dụng còn lại của gói cũ. Đội ngũ kỹ thuật sẽ hỗ trợ bạn chuyển đổi dữ liệu và nâng cấp các tính năng mới một cách liền mạch.</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact CTA -->
                            <div class="mt-10 text-center">
                                <p class="text-gray-600 mb-4">Bạn vẫn còn thắc mắc? Chúng tôi luôn sẵn lòng hỗ trợ!</p>
                                <button x-data @click="$store.modal.open()" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-emerald-600 hover:bg-emerald-700 hover:cursor-pointer focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500">
                                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                                    </svg>
                                    Liên hệ hỗ trợ
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column - Template Info -->
                    <div class="md:col-span-1">
                        <div class="sticky top-24">
                            <!-- Template Header -->
                            <div class="pb-6">
                                <div class="flex items-center gap-2 mb-2">
                                    @if ($template->is_featured)
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-medium bg-gradient-to-r from-pink-500 to-rose-500 text-white">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1"
                                                viewBox="0 0 20 20" fill="currentColor">
                                                <path
                                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                            </svg>
                                            Nổi bật
                                        </span>
                                    @elseif($template->created_at->diffInDays() < 30)
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-medium bg-gradient-to-r from-blue-500 to-cyan-500 text-white">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1"
                                                viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd"
                                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                                                    clip-rule="evenodd" />
                                            </svg>
                                            Mới
                                        </span>
                                    @endif
                                </div>
                                <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ $template->name }}</h1>

                                <!-- Price and Actions -->
                                <div class="mt-2">
                                    <div class="flex items-baseline mb-4">
                                        @if ($template->has_sale_off)
                                            <span
                                                class="text-3xl font-bold text-emerald-600">{{ number_format($template->sale_price) }}₫</span>
                                            <span
                                                class="ml-2 text-lg text-gray-500 line-through">{{ number_format($template->original_price) }}₫</span>
                                            <span
                                                class="ml-2 bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">
                                                -{{ 100 - round(($template->sale_price / $template->original_price) * 100) }}%
                                            </span>
                                        @else
                                            <span
                                                class="text-3xl font-bold text-emerald-600">{{ number_format($template->price) }}₫</span>
                                        @endif
                                    </div>

                                    <!-- Main CTA Section -->
                                    <div
                                        class="mt-6 mb-8 p-6 bg-gradient-to-br from-emerald-50 to-blue-50 rounded-xl border border-emerald-100 shadow-sm">
                                        <h3 class="text-lg font-semibold text-gray-900 text-center mb-4">Bạn muốn sử dụng
                                            mẫu này?</h3>
                                        <div class="space-y-4">
                                            <!-- Primary CTA Button -->
                                            <button
                                                x-data
                                                @click="$store.trialModal.open({{ $template->id }}, '{{ $template->name }}')"
                                                type="button"
                                                class="w-full flex items-center justify-center px-6 py-4 border border-transparent text-lg font-semibold rounded-lg text-white bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:cursor-pointer hover:to-emerald-700 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg">
                                                <span class="mr-2">DÙNG THỬ MIỄN PHÍ</span>
                                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd"
                                                        d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                                                        clip-rule="evenodd"></path>
                                                </svg>
                                            </button>

                                            <!-- Demo Button -->
                                            @if ($template->demo_url)
                                                <a href="{{ route('demo.show', $template->slug) }}" target="_blank"
                                                    class="w-full flex items-center justify-center px-6 py-3 border border-gray-200 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors hover:border-blue-200 hover:text-blue-700">
                                                    <svg class="w-5 h-5 mr-2 text-blue-500" fill="none"
                                                        stroke="currentColor" viewBox="0 0 24 24"
                                                        xmlns="http://www.w3.org/2000/svg">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                                        </path>
                                                    </svg>
                                                    Xem Demo Trực Tiếp
                                                </a>
                                            @endif
                                        </div>
                                    </div>

                                    <!-- Secondary Actions -->
                                    <div class="border-t border-gray-200 pt-6">
                                        <div class="flex items-center justify-between space-x-4">
                                            <!-- Favorite Button -->
                                            <div class="flex-1 flex flex-col items-center justify-center p-3 border border-gray-200 rounded-lg bg-white hover:bg-gray-50 transition-colors hover:border-emerald-200 hover:text-emerald-700">
                                                <x-ui.favorite-button
                                                    :template="$template"
                                                    size="lg"
                                                    position="relative"
                                                    variant="minimal"
                                                    :show-tooltip="true"
                                                    :is-has-button-classes="false"
                                                />
                                                <span class="text-sm font-medium text-gray-700">Yêu thích</span>
                                            </div>

                                            <!-- Divider -->
                                            <div class="h-12 w-px bg-gray-200"></div>

                                            <!-- Chat Button -->
                                            <button type="button"
                                                class="flex-1 flex flex-col items-center justify-center p-3 border border-gray-200 rounded-lg bg-white hover:bg-gray-50 transition-colors hover:border-blue-200 hover:text-blue-700">
                                                <svg class="w-6 h-6 text-blue-500 mb-1" fill="none"
                                                    stroke="currentColor" viewBox="0 0 24 24"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z">
                                                    </path>
                                                </svg>
                                                <span class="text-sm font-medium text-gray-700">Chat ngay</span>
                                            </button>

                                            <!-- Divider -->
                                            <div class="h-12 w-px bg-gray-200"></div>

                                            <!-- Phone Button -->
                                            <a href="tel:{{ siteSettings()->phone }}"
                                                class="flex-1 flex flex-col items-center justify-center p-3 border border-gray-200 rounded-lg bg-white hover:bg-gray-50 transition-colors hover:border-emerald-200 hover:text-emerald-700">
                                                <svg class="w-6 h-6 text-emerald-500 mb-1" fill="none"
                                                    stroke="currentColor" viewBox="0 0 24 24"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z">
                                                    </path>
                                                </svg>
                                                <span class="text-sm font-medium text-gray-700">Gọi ngay</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <!-- Promotion Program -->
                                <div class="mt-3 bg-amber-50 border-l-4 border-amber-400 p-4 rounded-r-lg">
                                    <div class="ml-3">
                                        <div class="flex text-sm font-medium text-amber-800">
                                            <svg class="h-5 w-5 text-amber-400 mr-2" xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd"
                                                    d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                                                    clip-rule="evenodd" />
                                            </svg>
                                            <span>Chương trình khuyến mãi tháng 06/2026</span>
                                        </div>
                                        <div class="mt-2 text-sm text-amber-700 space-y-2">
                                            <div class="flex items-start">
                                                <span
                                                    class="inline-flex items-center justify-center h-5 w-5 rounded-full bg-amber-100 text-amber-800 text-xs font-medium mr-2 flex-shrink-0">1</span>
                                                <span>Miễn phí hosting và domain .com hoặc .net 1 năm</span>
                                            </div>
                                            <div class="flex items-start">
                                                <span
                                                    class="inline-flex items-center justify-center h-5 w-5 rounded-full bg-amber-100 text-amber-800 text-xs font-medium mr-2 flex-shrink-0">2</span>
                                                <span>Giảm 30% phí gia hạn hosting và domain khi đăng ký từ 2 năm</span>
                                            </div>
                                            <div class="flex items-start">
                                                <span
                                                    class="inline-flex items-center justify-center h-5 w-5 rounded-full bg-amber-100 text-amber-800 text-xs font-medium mr-2 flex-shrink-0">3</span>
                                                <span>Giảm 50% phí gia hạn hosting và domain khi đăng ký từ 3 năm</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Reviews -->
            {{-- <div class="mt-16">
                <h2 class="text-2xl font-bold text-gray-900 mb-8">Đánh giá từ khách hàng</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- Review 1 -->
                    <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                        <div class="flex items-center mb-4">
                            <div class="flex-shrink-0">
                                <div class="h-12 w-12 rounded-full bg-emerald-100 flex items-center justify-center text-emerald-600 font-bold text-xl">
                                    T
                                </div>
                            </div>
                            <div class="ml-4">
                                <h4 class="font-medium text-gray-900">Trần Văn A</h4>
                                <div class="flex items-center mt-1">
                                    <div class="flex text-amber-400">
                                        @for ($i = 1; $i <= 5; $i++)
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                            </svg>
                                        @endfor
                                    </div>
                                </div>
                            </div>
                        </div>
                        <p class="text-gray-600">"Mẫu website rất đẹp và chuyên nghiệp. Đội ngũ hỗ trợ nhiệt tình, tư vấn kỹ lưỡng. Tôi rất hài lòng với sản phẩm và dịch vụ của CSlant."</p>
                        <div class="mt-3 text-sm text-gray-500">Đã sử dụng gói: <span class="font-medium">Business Web</span></div>
                    </div>

                    <!-- Review 2 -->
                    <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                        <div class="flex items-center mb-4">
                            <div class="flex-shrink-0">
                                <div class="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-bold text-xl">
                                    N
                                </div>
                            </div>
                            <div class="ml-4">
                                <h4 class="font-medium text-gray-900">Nguyễn Thị B</h4>
                                <div class="flex items-center mt-1">
                                    <div class="flex text-amber-400">
                                        @for ($i = 1; $i <= 5; $i++)
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                            </svg>
                                        @endfor
                                    </div>
                                </div>
                            </div>
                        </div>
                        <p class="text-gray-600">"Tôi đã tìm kiếm rất nhiều nơi và quyết định chọn CSlant. Không hề thất vọng! Giao diện đẹp, tối ưu tốt cho di động và load cực nhanh."</p>
                        <div class="mt-3 text-sm text-gray-500">Đã sử dụng gói: <span class="font-medium">Premium Web</span></div>
                    </div>
                </div>
            </div> --}}

            <!-- Related Templates -->
            @if ($relatedTemplates->count() > 0)
                <div class="mt-16">
                    <div class="flex justify-between items-center mb-8">
                        <h2 class="text-2xl font-bold text-gray-900">Mẫu website {{ $template->category->name }} khác</h2>
                        <a href="{{ route('templates.category', $template->category->slug) }}"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-emerald-700 bg-emerald-100 hover:bg-emerald-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500">
                            Xem tất cả
                            <svg class="ml-2 -mr-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd"
                                    d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                                    clip-rule="evenodd"></path>
                            </svg>
                        </a>
                    </div>

                    <div class="relative">
                        <div class="related-templates-swiper swiper">
                            <div class="swiper-wrapper pb-10">
                                @foreach ($relatedTemplates as $related)
                                <x-card.template-card :template="$related" />
                                @endforeach
                            </div>
                            <!-- Add navigation arrows -->
                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                            <!-- Add pagination -->
                            <div class="swiper-pagination"></div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- You May Also Like Templates -->
            @if (isset($randomTemplates) && $randomTemplates->count() > 0)
                <div class="my-16">
                    <div class="flex justify-between items-center mb-8">
                        <h2 class="text-2xl font-bold text-gray-900">Mẫu website bạn có thể thích</h2>
                    </div>

                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        @foreach ($randomTemplates as $randomTemplate)
                        <x-card.template-card :template="$randomTemplate" />
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </div>
@endsection

@push('scripts')
    @vite(['resources/js/pages/templates/show.js'])
@endpush
