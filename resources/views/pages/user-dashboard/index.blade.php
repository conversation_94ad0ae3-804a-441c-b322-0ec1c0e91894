@extends('layouts.default')

@section('content')
    <div class="container mx-auto py-8">
        <h1 class="text-3xl font-extrabold mb-8 text-center text-emerald-600 tracking-tight">{{ __('messages.pages.user_details') }}</h1>
        <div x-data="{ tab: 'demo' }">
            <div class="mb-6 border-b border-gray-200 flex justify-center">
                <nav class="flex space-x-2 bg-gray-100 rounded-lg p-1 mb-1" aria-label="Tabs">
                    <button @click="tab = 'demo'"
                        :class="tab === 'demo' ? 'bg-emerald-600 text-white shadow' :
                            'text-emerald-600 hover:text-emerald-700'"
                        class="px-6 py-2 rounded-lg font-semibold focus:outline-none transition-all duration-200">{{ __('messages.pages.demo_pages') }}</button>
                    <button @click="tab = 'account'"
                        :class="tab === 'account' ? 'bg-emerald-600 text-white shadow' :
                            'text-emerald-600 hover:text-emerald-700'"
                        class="px-6 py-2 rounded-lg font-semibold focus:outline-none transition-all duration-200">{{ __('messages.pages.account_details') }}</button>
                    <button @click="tab = 'company'"
                        :class="tab === 'company' ? 'bg-emerald-600 text-white shadow' :
                            'text-emerald-600 hover:text-emerald-700'"
                        class="px-6 py-2 rounded-lg font-semibold focus:outline-none transition-all duration-200">{{ __('messages.pages.company_details') }}</button>
                </nav>
            </div>
            <!-- Websites Tab -->
            <div x-show="tab === 'demo'" x-transition x-data="websiteManager()">
                <div class="flex justify-end mb-4">
                    <button @click="showCreateModal = true"
                        class="flex items-center gap-2 bg-gradient-to-r from-emerald-500 to-emerald-700 text-white px-5 py-2 rounded-lg shadow hover:from-emerald-600 hover:to-emerald-800 transition font-semibold">
                        <svg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' fill='none' viewBox='0 0 24 24'
                            stroke='currentColor'>
                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 4v16m8-8H4' />
                        </svg>
                        {{ __('messages.website.create_demo') }}
                    </button>
                </div>
                <div class="overflow-x-auto rounded-lg shadow">
                    <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                        <thead class="bg-emerald-50">
                            <tr>
                                <th class="px-6 py-3 border-b text-left text-xs font-bold text-emerald-700 uppercase">{{ __('messages.pages.id') }}</th>
                                <th class="px-6 py-3 border-b text-left text-xs font-bold text-emerald-700 uppercase">{{ __('messages.website.name') }}</th>
                                <th class="px-6 py-3 border-b text-left text-xs font-bold text-emerald-700 uppercase">{{ __('messages.website.domain') }}</th>
                                <th class="px-6 py-3 border-b text-left text-xs font-bold text-emerald-700 uppercase">{{ __('messages.website.status') }}</th>
                                <th class="px-6 py-3 border-b text-left text-xs font-bold text-emerald-700 uppercase">{{ __('messages.website.stage') }}</th>
                                <th class="px-6 py-3 border-b text-left text-xs font-bold text-emerald-700 uppercase">{{ __('messages.pages.actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($websites as $website)
                                <tr class="hover:bg-emerald-50 transition">
                                    <td class="px-6 py-4 border-b">{{ $website->id }}</td>
                                    <td class="px-6 py-4 border-b font-semibold text-emerald-700">{{ $website->name }}</td>
                                    <td class="px-6 py-4 border-b text-emerald-600">{{ $website->domain }}</td>
                                    <td class="px-6 py-4 border-b">
                                        <span class="px-2 py-1 text-xs font-medium rounded-full
                                            @if($website->status === 'demo') bg-blue-100 text-blue-800
                                            @elseif($website->status === 'trial') bg-yellow-100 text-yellow-800
                                            @elseif($website->status === 'active') bg-green-100 text-green-800
                                            @elseif($website->status === 'suspended') bg-red-100 text-red-800
                                            @else bg-gray-100 text-gray-800 @endif">
                                            {{ ucfirst($website->status) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 border-b">
                                        <span class="px-2 py-1 text-xs font-medium rounded-full
                                            @if($website->lifecycle_stage === 'demo') bg-blue-100 text-blue-800
                                            @elseif($website->lifecycle_stage === 'trial') bg-yellow-100 text-yellow-800
                                            @else bg-green-100 text-green-800 @endif">
                                            {{ ucfirst($website->lifecycle_stage) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 border-b space-x-2">
                                        <a href="{{ $website->getUrl() }}" target="_blank"
                                            class="inline-flex items-center text-emerald-600 hover:text-emerald-800 font-medium transition">
                                            <svg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5 mr-1' fill='none'
                                                viewBox='0 0 24 24' stroke='currentColor'>
                                                <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2'
                                                    d='M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-2M7 7l10 10M17 7v4' />
                                            </svg>Xem
                                        </a>
                                        <button @click="editWebsite({{ $website->id }}, '{{ $website->name }}', '{{ addslashes($website->demo_content ?? '') }}')"
                                            class="inline-flex items-center text-yellow-600 hover:text-yellow-800 font-medium transition">
                                            <svg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5 mr-1' fill='none'
                                                viewBox='0 0 24 24' stroke='currentColor'>
                                                <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2'
                                                    d='M15.232 5.232l3.536 3.536M9 13h3l8-8a2.828 2.828 0 00-4-4l-8 8v3zm0 0v3a2 2 0 002 2h3' />
                                            </svg>Sửa
                                        </button>
                                        @if($website->isDemo())
                                            <button @click="convertToTrial({{ $website->id }})"
                                                class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium transition">
                                                <svg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5 mr-1' fill='none'
                                                    viewBox='0 0 24 24' stroke='currentColor'>
                                                    <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2'
                                                        d='M13 10V3L4 14h7v7l9-11h-7z' />
                                                </svg>Trial
                                            </button>
                                        @endif
                                        @if($website->isDemo() || $website->isTrial())
                                            <button @click="convertToProduction({{ $website->id }})"
                                                class="inline-flex items-center text-purple-600 hover:text-purple-800 font-medium transition">
                                                <svg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5 mr-1' fill='none'
                                                    viewBox='0 0 24 24' stroke='currentColor'>
                                                    <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2'
                                                        d='M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z' />
                                                </svg>Upgrade
                                            </button>
                                        @endif
                                        <button @click="deleteWebsite({{ $website->id }})"
                                            class="inline-flex items-center text-red-600 hover:text-red-800 font-medium transition">
                                            <svg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5 mr-1' fill='none'
                                                viewBox='0 0 24 24' stroke='currentColor'>
                                                <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2'
                                                    d='M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16' />
                                            </svg>Xóa
                                        </button>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                                        {{ __('messages.website.no_websites') }}
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- Account Details Tab -->
            <div x-show="tab === 'account'" x-transition>
                <form method="POST" action="{{ route('user-dashboard.update') }}"
                    class="max-w-3xl mx-auto bg-white p-8 rounded-xl shadow-lg mt-8 border border-emerald-200">
                    @csrf
                    @method('POST')
                    @if (session('success'))
                        <div
                            class="mb-4 p-3 rounded bg-emerald-100 text-emerald-700 border border-emerald-300 text-center">
                            {{ session('success') }}
                        </div>
                    @endif
                    @if ($errors->any())
                        <div class="mb-4 p-3 rounded bg-red-100 text-red-700 border border-red-300">
                            <ul class="list-disc pl-5">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="name">Tên</label>
                            <div class="relative">
                                <input
                                    class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                    id="name" name="name" type="text"
                                    value="{{ old('name', $user->name ?? '') }}" maxlength="255" required>
                                <span class="absolute right-3 top-2.5 text-emerald-400">
                                    <svg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' fill='none'
                                        viewBox='0 0 24 24' stroke='currentColor'>
                                        <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2'
                                            d='M5.121 17.804A13.937 13.937 0 0112 15c2.5 0 4.847.655 6.879 1.804M15 11a3 3 0 11-6 0 3 3 0 016 0z' />
                                    </svg>
                                </span>
                            </div>
                            @error('name')
                                <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="email">Email</label>
                            <div class="relative">
                                <input
                                    class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                    id="email" name="email" type="email"
                                    value="{{ old('email', $user->email ?? '') }}" required
                                    pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$" maxlength="255"
                                    title="Vui lòng nhập đúng định dạng email">
                                @error('email')
                                    <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                                @enderror
                                <span class="absolute right-3 top-2.5 text-emerald-400">
                                    <svg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' fill='none'
                                        viewBox='0 0 24 24' stroke='currentColor'>
                                        <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2'
                                            d='M16 12H8m8 0a8 8 0 11-16 0 8 8 0 0116 0z' />
                                    </svg>
                                </span>
                            </div>
                        </div>
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="password">Mật khẩu mới</label>
                            <div class="relative">
                                <input
                                    class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                    id="password" name="password" type="password" placeholder="Nhập mật khẩu mới"
                                    autocomplete="new-password" minlength="6">
                                @error('password')
                                    <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                                @enderror
                                @error('password_confirmation')
                                    <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                                @enderror
                                <span class="absolute right-3 top-2.5 text-emerald-400">
                                    <svg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' fill='none'
                                        viewBox='0 0 24 24' stroke='currentColor'>
                                        <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2'
                                            d='M12 11c0-1.104.896-2 2-2s2 .896 2 2-.896 2-2 2-2-.896-2-2zm0 0v2m0 4h.01' />
                                    </svg>
                                </span>
                            </div>
                        </div>
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="password_confirmation">Xác nhận mật
                                khẩu mới</label>
                            <div class="relative">
                                <input
                                    class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                    id="password_confirmation" name="password_confirmation" type="password"
                                    placeholder="Nhập lại mật khẩu mới" autocomplete="new-password">
                                <span class="absolute right-3 top-2.5 text-emerald-400">
                                    <svg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' fill='none'
                                        viewBox='0 0 24 24' stroke='currentColor'>
                                        <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2'
                                            d='M12 11c0-1.104.896-2 2-2s2 .896 2 2-.896 2-2 2-2-.896-2-2zm0 0v2m0 4h.01' />
                                    </svg>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-end mt-6">
                        <button type="submit"
                            class="bg-gradient-to-r from-emerald-500 to-emerald-700 text-white px-6 py-2 rounded-lg font-bold shadow hover:from-emerald-600 hover:to-emerald-800 transition">Cập
                            nhật</button>
                    </div>
                    <script>
                        function validatePasswordConfirmation() {
                            const password = document.getElementById('password').value;
                            const confirm = document.getElementById('password_confirmation').value;
                            if (password !== confirm) {
                                alert('Mật khẩu xác nhận không khớp!');
                                return false;
                            }
                            return true;
                        }
                        // Attach validation to form submit
                        document.addEventListener('DOMContentLoaded', function() {
                            const form = document.querySelector('form');
                            if (form) {
                                form.onsubmit = validatePasswordConfirmation;
                            }
                        });
                    </script>
                </form>
            </div>
            <!-- Company Details Tab (custom fields) -->
            <div x-show="tab === 'company'" x-transition>
                <form method="POST" action="{{ route('user-dashboard.update-company') }}"
                    class="max-w-3xl mx-auto bg-white p-8 rounded-xl shadow-lg mt-8 border border-emerald-200">
                    @csrf
                    @if (session('success'))
                        <div
                            class="mb-4 p-3 rounded bg-emerald-100 text-emerald-700 border border-emerald-300 text-center">
                            {{ session('success') }}
                        </div>
                    @endif
                    @if ($errors->any())
                        <div class="mb-4 p-3 rounded bg-red-100 text-red-700 border border-red-300">
                            <ul class="list-disc pl-5">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="company_name">Tên công ty</label>
                            <input
                                class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                id="company_name" name="company_name" type="text"
                                value="{{ old('company_name', $company->business_name ?? '') }}" maxlength="255"
                                required>
                            @error('company_name')
                                <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="company_tax">Mã số thuế</label>
                            <input
                                class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                id="company_tax" name="company_tax" type="text"
                                value="{{ old('company_tax', $company->tax_code ?? '') }}" maxlength="100">
                            @error('company_tax')
                                <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="company_contact">Người liên
                                hệ</label>
                            <input
                                class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                id="company_contact" name="company_contact" type="text"
                                value="{{ old('company_contact', $company->contact_person ?? '') }}" maxlength="255">
                            @error('company_contact')
                                <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="company_phone">Số điện thoại</label>
                            <input
                                class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                id="company_phone" name="company_phone" type="text"
                                value="{{ old('company_phone', $company->phone ?? '') }}" pattern="^\+?[0-9 .-]{8,20}$"
                                maxlength="50" title="Vui lòng nhập đúng định dạng số điện thoại">
                            @error('company_phone')
                                <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="company_email">Địa chỉ email</label>
                            <input
                                class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                id="company_email" name="company_email" type="email"
                                value="{{ old('company_email', $company->email ?? '') }}"
                                pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$" maxlength="255"
                                title="Vui lòng nhập đúng định dạng email">
                            @error('company_email')
                                <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                            @enderror
                            <script>
                                // Validate email and phone for Account Details
                                document.addEventListener('DOMContentLoaded', function() {
                                    const accForm = document.querySelector('form[action="{{ route('user-dashboard.update') }}"]');
                                    if (accForm) {
                                        accForm.addEventListener('submit', function(e) {
                                            const email = accForm.querySelector('#email');
                                            const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                                            if (email && !emailPattern.test(email.value)) {
                                                alert('Vui lòng nhập đúng định dạng email.');
                                                email.focus();
                                                e.preventDefault();
                                                return false;
                                            }
                                            return true;
                                        });
                                    }
                                    const companyForm = document.querySelector(
                                        'form[action="{{ route('user-dashboard.update-company') }}"]');
                                    if (companyForm) {
                                        companyForm.addEventListener('submit', function(e) {
                                            const email = companyForm.querySelector('#company_email');
                                            const phone = companyForm.querySelector('#company_phone');
                                            const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                                            const phonePattern = /^\+?[0-9 .-]{8,20}$/;
                                            if (email && email.value && !emailPattern.test(email.value)) {
                                                alert('Vui lòng nhập đúng định dạng email.');
                                                email.focus();
                                                e.preventDefault();
                                                return false;
                                            }
                                            if (phone && phone.value && !phonePattern.test(phone.value)) {
                                                alert('Vui lòng nhập đúng định dạng số điện thoại.');
                                                phone.focus();
                                                e.preventDefault();
                                                return false;
                                            }
                                            return true;
                                        });
                                    }
                                });
                            </script>
                        </div>
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="company_address">Địa chỉ công
                                ty</label>
                            <input
                                class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                id="company_address" name="company_address" type="text"
                                value="{{ old('company_address', $company->address ?? '') }}" maxlength="255">
                            @error('company_address')
                                <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="company_city">Thành phố</label>
                            <input
                                class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                id="company_city" name="company_city" type="text"
                                value="{{ old('company_city', $company->city ?? '') }}" maxlength="100">
                            @error('company_city')
                                <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label class="block text-emerald-700 font-bold mb-2" for="company_country">Quốc gia</label>
                            <input
                                class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                id="company_country" name="company_country" type="text"
                                value="{{ old('company_country', $company->country ?? '') }}" maxlength="100">
                            @error('company_country')
                                <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-emerald-700 font-bold mb-2" for="company_website">Website công
                                ty</label>
                            <input
                                class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-600 transition text-emerald-700"
                                id="company_website" name="company_website" type="text"
                                value="{{ old('company_website', $company->website ?? '') }}" maxlength="255">
                            @error('company_website')
                                <p class="text-red-600 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                    <div class="flex justify-end mt-6">
                        <button type="submit"
                            class="bg-gradient-to-r from-emerald-500 to-emerald-700 text-white px-6 py-2 rounded-lg font-bold shadow hover:from-emerald-600 hover:to-emerald-800 transition">Cập
                            nhật</button>
                    </div>
                </form>
            </div>

            <!-- Demo Page Modal -->
            <div x-show="showCreateModal || showEditModal"
                 x-transition:enter="ease-out duration-300"
                 x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100"
                 x-transition:leave="ease-in duration-200"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0"
                 class="fixed inset-0 z-50 overflow-y-auto"
                 style="display: none;">
                <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

                    <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                        <form @submit.prevent="showCreateModal ? createDemoPage() : updateDemoPage()">
                            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4" x-text="showCreateModal ? 'Tạo trang demo mới' : 'Chỉnh sửa trang demo'"></h3>

                                <div class="mb-4">
                                    <label for="demo_page_name" class="block text-sm font-medium text-gray-700 mb-2">Tên trang</label>
                                    <input type="text" id="demo_page_name" x-model="form.name" required
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                                </div>

                                <div class="mb-4">
                                    <label for="demo_page_content" class="block text-sm font-medium text-gray-700 mb-2">Nội dung</label>
                                    <textarea id="demo_page_content" x-model="form.content" rows="4"
                                              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                                              placeholder="Nhập nội dung trang demo..."></textarea>
                                </div>
                            </div>

                            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                                <button type="submit" :disabled="loading"
                                        class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-emerald-600 text-base font-medium text-white hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50">
                                    <span x-show="!loading" x-text="showCreateModal ? 'Tạo' : 'Cập nhật'"></span>
                                    <span x-show="loading">Đang xử lý...</span>
                                </button>
                                <button type="button" @click="closeModal()"
                                        class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                                    Hủy
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function websiteManager() {
            return {
                showCreateModal: false,
                showEditModal: false,
                loading: false,
                editingId: null,
                form: {
                    name: '',
                    demo_content: '',
                    purpose: '',
                    detail: ''
                },

                createWebsite() {
                    this.loading = true;

                    fetch('{{ route("websites.store") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify(this.form)
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert(data.message || 'Có lỗi xảy ra');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Có lỗi xảy ra khi tạo website demo');
                    })
                    .finally(() => {
                        this.loading = false;
                    });
                },

                editWebsite(id, name, content) {
                    this.editingId = id;
                    this.form.name = name;
                    this.form.demo_content = content;
                    this.showEditModal = true;
                },

                updateWebsite() {
                    this.loading = true;

                    fetch(`/websites/${this.editingId}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify(this.form)
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert(data.message || 'Có lỗi xảy ra');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Có lỗi xảy ra khi cập nhật website');
                    })
                    .finally(() => {
                        this.loading = false;
                    });
                },

                convertToTrial(id) {
                    if (!confirm('Chuyển đổi demo thành trial 14 ngày miễn phí?')) {
                        return;
                    }

                    fetch(`/websites/${id}/convert-to-trial`, {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert(data.message || 'Có lỗi xảy ra');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Có lỗi xảy ra khi chuyển đổi trial');
                    });
                },

                convertToProduction(id) {
                    if (!confirm('Chuyển đổi thành website thật với gói Basic?')) {
                        return;
                    }

                    fetch(`/websites/${id}/convert-to-production`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            plan_name: 'basic',
                            plan_type: 'monthly',
                            price: 29.99
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert(data.message || 'Có lỗi xảy ra');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Có lỗi xảy ra khi chuyển đổi production');
                    });
                },

                deleteWebsite(id) {
                    if (!confirm('Bạn có chắc chắn muốn xóa website này?')) {
                        return;
                    }

                    fetch(`/websites/${id}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert(data.message || 'Có lỗi xảy ra');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Có lỗi xảy ra khi xóa website');
                    });
                },

                closeModal() {
                    this.showCreateModal = false;
                    this.showEditModal = false;
                    this.editingId = null;
                    this.form = { name: '', demo_content: '', purpose: '', detail: '' };
                }
            }
        }
    </script>
@endsection
