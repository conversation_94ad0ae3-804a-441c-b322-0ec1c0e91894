<x-mail::message>
# {{ __('messages.email.email_changed_subject') }}

{{ __('messages.email.email_changed_greeting', ['name' => $user->name]) }}

{{ __('messages.email.email_changed_message') }}

**{{ __('messages.email.email_changed_old_email') }}** {{ $oldEmail }}
**{{ __('messages.email.email_changed_new_email') }}** {{ $user->email }}
**{{ __('messages.email.email_changed_time') }}** {{ $changeTime }}

{{ __('messages.email.email_changed_verification_note') }}

{{ __('messages.email.email_changed_security_note') }}

<x-mail::button :url="route('login')">
{{ __('messages.email.email_changed_login_button') }}
</x-mail::button>

{{ __('messages.email.email_changed_thanks') }}<br>
{{ config('app.name') }}
</x-mail::message>
