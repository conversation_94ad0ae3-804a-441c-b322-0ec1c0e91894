APP_NAME="cslant.net"
APP_ENV=local
APP_KEY=base64:osWkhizGnDRMZDnri/zJACsrqYOr2RBfjsuZYoh8nHY=
APP_DEBUG=true
APP_URL=http://${CSLANT_NET_DOMAIN}:${NGINX_HOST_HTTP_PORT}

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US
APP_FORCE_DEFAULT_LOCALE=false

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
# If you use Laravel Sail, just change DB_HOST to DB_HOST=mysql
# On some hosting DB_HOST can be localhost instead of 127.0.0.1
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=cslant-net
DB_USERNAME=root
DB_PASSWORD=${MYSQL_ROOT_PASSWORD}

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

GOOGLE_TAG_MANAGER_ID=GTM-56RL2P5S
GOOGLE_TAG_MANAGER_ENABLED=false

CSL_99_SSH_HOST=127.0.0.1
CSL_99_SSH_USER=cslant
CSL_99_SSH_PRIVATE_KEY_PATH=/home/<USER>/.ssh/id_rsa
CSL_99_SSH_PORT=22

SENTRY_LARAVEL_DSN=https://<EMAIL>/4509592872026112
SENTRY_SEND_DEFAULT_PII=true

# vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/config/clockwork.php
CLOCKWORK_ENABLE=false
CLOCKWORK_AUTHENTICATION=false
CLOCKWORK_AUTHENTICATION_PASSWORD=
