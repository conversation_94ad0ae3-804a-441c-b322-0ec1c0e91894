<?php

namespace App\Models;

use App\Enums\WebsiteStatusEnum;
use App\Enums\WebsiteLifecycleEnum;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

/**
 * @property int $id
 * @property int $customer_user_id
 * @property string $name
 * @property string $phone
 * @property string $company
 * @property string $domain
 * @property string $package Gói đang sử dụng, vd: express web basic, express web plus, pro web....
 * @property null|string $purpose
 * @property null|string $detail
 * @property string $status
 * @property string $lifecycle_stage
 * @property null|int $subscription_id
 * @property null|Carbon $demo_expires_at
 * @property null|Carbon $trial_expires_at
 * @property null|string $demo_content
 * @property null|int $template_id
 * @property null|string $template_name
 * @property null|string $note
 * @property null|string $admin_username
 * @property null|string $admin_password
 * @property null|string $customer_username
 * @property null|string $customer_password
 * @property null|string $deploy_logs
 * @property null|Carbon $created_at
 * @property null|Carbon $updated_at
 * @property null|string $deleted_at
 * @property-read null|Template $template
 * @property-read null|CustomerUser $customerUser
 * @property-read null|Subscription $subscription
 *
 * @method static Builder<static>|Website newModelQuery()
 * @method static Builder<static>|Website newQuery()
 * @method static Builder<static>|Website query()
 * @method static Builder<static>|Website whereAdminPassword($value)
 * @method static Builder<static>|Website whereAdminUsername($value)
 * @method static Builder<static>|Website whereCompany($value)
 * @method static Builder<static>|Website whereCreatedAt($value)
 * @method static Builder<static>|Website whereCustomerPassword($value)
 * @method static Builder<static>|Website whereCustomerUsername($value)
 * @method static Builder<static>|Website whereDeletedAt($value)
 * @method static Builder<static>|Website whereDeployLogs($value)
 * @method static Builder<static>|Website whereDetail($value)
 * @method static Builder<static>|Website whereDomain($value)
 * @method static Builder<static>|Website whereId($value)
 * @method static Builder<static>|Website whereName($value)
 * @method static Builder<static>|Website whereNote($value)
 * @method static Builder<static>|Website wherePackage($value)
 * @method static Builder<static>|Website wherePhone($value)
 * @method static Builder<static>|Website wherePurpose($value)
 * @method static Builder<static>|Website whereStatus($value)
 * @method static Builder<static>|Website whereTemplateId($value)
 * @method static Builder<static>|Website whereTemplateName($value)
 * @method static Builder<static>|Website whereUpdatedAt($value)
 * @method static Builder<static>|Website whereCustomerUserId($value)
 *
 * @mixin Builder
 * @mixin \Eloquent
 */
class Website extends Model
{


    protected $fillable = [
        'customer_user_id',
        'name',
        'phone',
        'company',
        'purpose',
        'domain',
        'package',
        'purpose',
        'detail',
        'status',
        'lifecycle_stage',
        'subscription_id',
        'demo_expires_at',
        'trial_expires_at',
        'demo_content',
        'template_id',
        'template_name',
        'note',
        'admin_username',
        'admin_password',
        'customer_username',
        'customer_password',
        'deploy_logs',
    ];

    protected $casts = [
        'status' => WebsiteStatusEnum::class,
        'lifecycle_stage' => WebsiteLifecycleEnum::class,
        'demo_expires_at' => 'datetime',
        'trial_expires_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the customer user that owns the website.
     *
     * @return BelongsTo<CustomerUser, $this>
     */
    public function customerUser(): BelongsTo
    {
        return $this->belongsTo(CustomerUser::class);
    }

    /**
     * Get the template that the website is using.
     *
     * @return BelongsTo<Template, $this>
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(Template::class);
    }

    /**
     * Get the subscription for the website.
     *
     * @return BelongsTo<Subscription, $this>
     */
    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    /**
     * Check if website is in demo stage.
     */
    public function isDemo(): bool
    {
        return $this->lifecycle_stage === WebsiteLifecycleEnum::DEMO;
    }

    /**
     * Check if website is in trial stage.
     */
    public function isTrial(): bool
    {
        return $this->lifecycle_stage === WebsiteLifecycleEnum::TRIAL;
    }

    /**
     * Check if website is in production stage.
     */
    public function isProduction(): bool
    {
        return $this->lifecycle_stage === WebsiteLifecycleEnum::PRODUCTION;
    }

    /**
     * Check if demo is expired.
     */
    public function isDemoExpired(): bool
    {
        return $this->demo_expires_at && $this->demo_expires_at <= now();
    }

    /**
     * Check if trial is expired.
     */
    public function isTrialExpired(): bool
    {
        return $this->trial_expires_at && $this->trial_expires_at <= now();
    }

    /**
     * Get days remaining for demo.
     */
    public function getDemoRemainingDays(): int
    {
        if (!$this->demo_expires_at) {
            return 0;
        }

        return max(0, now()->diffInDays($this->demo_expires_at, false));
    }

    /**
     * Get days remaining for trial.
     */
    public function getTrialRemainingDays(): int
    {
        if (!$this->trial_expires_at) {
            return 0;
        }

        return max(0, now()->diffInDays($this->trial_expires_at, false));
    }

    /**
     * Convert demo to trial.
     */
    public function convertToTrial(int $trialDays = 14): bool
    {
        if (!$this->isDemo()) {
            return false;
        }

        $this->update([
            'lifecycle_stage' => WebsiteLifecycleEnum::TRIAL,
            'status' => WebsiteStatusEnum::TRIAL,
            'trial_expires_at' => now()->addDays($trialDays),
        ]);

        return true;
    }

    /**
     * Convert to production with subscription.
     */
    public function convertToProduction(array $subscriptionData): bool
    {
        if ($this->isProduction()) {
            return false;
        }

        DB::transaction(function () use ($subscriptionData) {
            // Create subscription
            $subscription = Subscription::createWithFeatures($subscriptionData);

            // Update website
            $this->update([
                'lifecycle_stage' => WebsiteLifecycleEnum::PRODUCTION,
                'status' => WebsiteStatusEnum::ACTIVE,
                'subscription_id' => $subscription->id,
            ]);
        });

        return true;
    }

    /**
     * Get current plan name.
     */
    public function getCurrentPlan(): ?string
    {
        return $this->subscription?->plan_name;
    }

    /**
     * Get website URL.
     */
    public function getUrl(): string
    {
        $protocol = $this->isProduction() ? 'https' : 'http';
        return $protocol . '://' . $this->domain;
    }

    /**
     * Get status badge color.
     */
    public function getStatusColor(): string
    {
        return $this->status->color();
    }

    /**
     * Get status badge CSS classes.
     */
    public function getStatusBadgeClasses(): string
    {
        return $this->status->badgeClasses();
    }

    /**
     * Get lifecycle badge CSS classes.
     */
    public function getLifecycleBadgeClasses(): string
    {
        return $this->lifecycle_stage->badgeClasses();
    }
}
