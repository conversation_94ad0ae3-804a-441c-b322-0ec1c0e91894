<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $customer_user_id
 * @property string $name
 * @property string $phone
 * @property string $company
 * @property string $domain
 * @property string $package Gói đang sử dụng, vd: express web basic, express web plus, pro web....
 * @property null|string $purpose
 * @property null|string $detail
 * @property string $status
 * @property bool $is_demo
 * @property null|string $demo_content
 * @property null|int $template_id
 * @property null|string $template_name
 * @property null|string $note
 * @property null|string $admin_username
 * @property null|string $admin_password
 * @property null|string $customer_username
 * @property null|string $customer_password
 * @property null|string $deploy_logs
 * @property null|Carbon $created_at
 * @property null|Carbon $updated_at
 * @property null|string $deleted_at
 * @property-read null|Template $template
 * @property-read null|CustomerUser $customerUser
 *
 * @method static Builder<static>|Website newModelQuery()
 * @method static Builder<static>|Website newQuery()
 * @method static Builder<static>|Website query()
 * @method static Builder<static>|Website whereAdminPassword($value)
 * @method static Builder<static>|Website whereAdminUsername($value)
 * @method static Builder<static>|Website whereCompany($value)
 * @method static Builder<static>|Website whereCreatedAt($value)
 * @method static Builder<static>|Website whereCustomerPassword($value)
 * @method static Builder<static>|Website whereCustomerUsername($value)
 * @method static Builder<static>|Website whereDeletedAt($value)
 * @method static Builder<static>|Website whereDeployLogs($value)
 * @method static Builder<static>|Website whereDetail($value)
 * @method static Builder<static>|Website whereDomain($value)
 * @method static Builder<static>|Website whereId($value)
 * @method static Builder<static>|Website whereName($value)
 * @method static Builder<static>|Website whereNote($value)
 * @method static Builder<static>|Website wherePackage($value)
 * @method static Builder<static>|Website wherePhone($value)
 * @method static Builder<static>|Website wherePurpose($value)
 * @method static Builder<static>|Website whereStatus($value)
 * @method static Builder<static>|Website whereTemplateId($value)
 * @method static Builder<static>|Website whereTemplateName($value)
 * @method static Builder<static>|Website whereUpdatedAt($value)
 * @method static Builder<static>|Website whereCustomerUserId($value)
 *
 * @mixin Builder
 * @mixin \Eloquent
 */
class Website extends Model
{
    public const STATUS_PENDING = 'pending';

    public const STATUS_ACTIVE = 'active';

    public const STATUS_INACTIVE = 'inactive';

    protected $fillable = [
        'customer_user_id',
        'name',
        'phone',
        'company',
        'purpose',
        'domain',
        'package',
        'purpose',
        'detail',
        'status',
        'is_demo',
        'demo_content',
        'template_id',
        'template_name',
        'note',
        'admin_username',
        'admin_password',
        'customer_username',
        'customer_password',
        'deploy_logs',
    ];

    protected $casts = [
        'is_demo' => 'boolean',
    ];

    /**
     * Get the customer user that owns the website.
     *
     * @return BelongsTo<CustomerUser, $this>
     */
    public function customerUser(): BelongsTo
    {
        return $this->belongsTo(CustomerUser::class);
    }

    /**
     * Get the template that the website is using.
     *
     * @return BelongsTo<Template, $this>
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(Template::class);
    }
}
