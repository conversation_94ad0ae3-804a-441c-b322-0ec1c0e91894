<?php

namespace App\Models;

use App\Enums\CustomerUserStatusEnum;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Laravel\Sanctum\PersonalAccessToken;
use Spatie\Permission\Traits\HasRoles;

/**
 * @property int $id
 * @property int $customer_id
 * @property string $name
 * @property string $email
 * @property string $password
 * @property bool $is_owner
 * @property null|\Illuminate\Support\Carbon $email_verified_at
 * @property-read null|Customer $customer
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read null|int $notifications_count
 * @property-read Collection<int, PersonalAccessToken> $tokens
 * @property-read null|int $tokens_count
 * @property-read Collection<int, TemplateFavorite> $templateFavorites
 * @property-read Collection<int, Template> $favoritedTemplates
 *
 * @method static Builder<static>|CustomerUser active()
 * @method static Builder<static>|CustomerUser owners()
 * @method static Builder<static>|CustomerUser newModelQuery()
 * @method static Builder<static>|CustomerUser newQuery()
 * @method static Builder<static>|CustomerUser query()
 *
 * @property CustomerUserStatusEnum $status
 * @property-read Collection<int, \Spatie\Permission\Models\Permission> $permissions
 * @property-read null|int $permissions_count
 * @property-read Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read null|int $roles_count
 * @property-read Collection<int, Website> $websites
 * @property-read null|int $websites_count
 * @property null|\Illuminate\Support\Carbon $deleted_at
 *
 * @method static Builder<static>|CustomerUser permission($permissions, $without = false)
 * @method static Builder<static>|CustomerUser role($roles, $guard = null, $without = false)
 * @method static Builder<static>|CustomerUser withoutPermission($permissions)
 * @method static Builder<static>|CustomerUser withoutRole($roles, $guard = null)
 * @method static \Database\Factories\CustomerUserFactory factory($count = null, $state = [])
 *
 * @property-read null|int $favorited_templates_count
 * @property-read null|int $template_favorites_count
 *
 * @method static Builder<static>|CustomerUser onlyTrashed()
 * @method static Builder<static>|CustomerUser withTrashed()
 * @method static Builder<static>|CustomerUser withoutTrashed()
 *
 * @mixin \Eloquent
 */
class CustomerUser extends Authenticatable implements FilamentUser, MustVerifyEmail
{
    use HasApiTokens;
    use HasFactory;
    use HasRoles;
    use Notifiable;
    use SoftDeletes;

    protected $fillable = [
        'customer_id',
        'name',
        'email',
        'password',
        'is_owner',
        'status',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array<string, mixed>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'is_owner' => 'boolean',
        'status' => CustomerUserStatusEnum::class,
    ];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the websites owned by this customer user.
     *
     * @return HasMany<Website, $this>
     */
    public function websites(): HasMany
    {
        return $this->hasMany(Website::class);
    }

    /**
     * Get the template favorites for this customer user.
     *
     * @return HasMany<TemplateFavorite, $this>
     */
    public function templateFavorites(): HasMany
    {
        return $this->hasMany(TemplateFavorite::class);
    }

    /**
     * Get the favorited templates for this customer user.
     *
     * @return BelongsToMany<Template, $this>
     */
    public function favoritedTemplates(): BelongsToMany
    {
        return $this->belongsToMany(Template::class, 'template_favorites')
            ->withTimestamps();
    }

    /**
     * The scope to filter active customer users. (->active())
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', CustomerUserStatusEnum::ACTIVE);
    }

    /**
     * The scope to filter owner customer users. (->owners())
     *
     * The is_owner field identifies the primary contact/account manager for a customer.
     * This is the main user who has full administrative privileges for the customer account.
     * Only one user per customer should have is_owner = true.
     */
    public function scopeOwners(Builder $query): Builder
    {
        return $query->where('is_owner', true);
    }

    /**
     * Check if this user is the owner of the customer account.
     */
    public function isOwner(): bool
    {
        return $this->is_owner === true;
    }

    /**
     * Check if this user can perform administrative actions for the customer.
     * Currently, only owners can perform admin actions, but this method allows
     * for future expansion of permissions.
     */
    public function canAdministrateCustomer(): bool
    {
        return $this->isOwner();
    }

    /**
     * Determine if the user can access the given Filament panel.
     */
    public function canAccessPanel(Panel $panel): bool
    {
        if ($panel->getId() === 'user') {
            return $this->status === CustomerUserStatusEnum::ACTIVE;
        }

        return false;
    }
}
