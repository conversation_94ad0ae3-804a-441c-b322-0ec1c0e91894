<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property string $business_name
 * @property null|string $tax_code
 * @property null|string $contact_person
 * @property string $phone
 * @property null|string $email
 * @property null|string $address
 * @property null|string $city
 * @property string $country
 * @property null|string $website
 * @property null|string $notes
 * @property string $status
 * @property null|int $assigned_to
 * @property null|Carbon $created_at
 * @property null|Carbon $updated_at
 * @property null|Carbon $deleted_at
 * @property-read null|User $assignedUser
 * @property-read Collection<int, Contact> $contacts
 * @property-read null|int $contacts_count
 * @property-read null|CustomerUser $customerUser
 * @property-read null|CustomerUser $owner
 * @property-read null|string $primary_contact_email
 *
 * @method static Builder<static>|Customer newModelQuery()
 * @method static Builder<static>|Customer newQuery()
 * @method static Builder<static>|Customer onlyTrashed()
 * @method static Builder<static>|Customer query()
 * @method static Builder<static>|Customer where($column, $operator = null, $value = null, $boolean = 'and')
 * @method static Builder<static>|Customer whereAddress($value)
 * @method static Builder<static>|Customer whereAssignedTo($value)
 * @method static Builder<static>|Customer whereBusinessName($value)
 * @method static Builder<static>|Customer whereCity($value)
 * @method static Builder<static>|Customer whereContactPerson($value)
 * @method static Builder<static>|Customer whereCountry($value)
 * @method static Builder<static>|Customer whereCreatedAt($value)
 * @method static Builder<static>|Customer whereDeletedAt($value)
 * @method static Builder<static>|Customer whereEmail($value)
 * @method static Builder<static>|Customer whereId($value)
 * @method static Builder<static>|Customer whereNotes($value)
 * @method static Builder<static>|Customer wherePhone($value)
 * @method static Builder<static>|Customer whereStatus($value)
 * @method static Builder<static>|Customer whereTaxCode($value)
 * @method static Builder<static>|Customer whereUpdatedAt($value)
 * @method static Builder<static>|Customer whereWebsite($value)
 * @method static Builder<static>|Customer withTrashed()
 * @method static Builder<static>|Customer withoutTrashed()
 *
 * @property-read null|int $customer_user_count
 *
 * @method static \Database\Factories\CustomerFactory factory($count = null, $state = [])
 *
 * @mixin \Eloquent
 */
class Customer extends Model
{
    use HasFactory;
    use SoftDeletes;

    /** @var array<int, string> */
    protected $fillable = [
        'business_name',
        'tax_code',
        'contact_person',
        'phone',
        'email',
        'address',
        'city',
        'country',
        'website',
        'notes',
        'status',
        'assigned_to',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, mixed>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the user assigned to this customer.
     *
     * @return BelongsTo<User, $this>
     */
    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get all contacts for the customer.
     *
     * @return HasMany<Contact, $this>
     */
    public function contacts(): HasMany
    {
        return $this->hasMany(Contact::class);
    }

    /**
     * Get all customer users associated with this customer.
     *
     * @return HasMany<CustomerUser, $this>
     */
    public function customerUser(): HasMany
    {
        return $this->hasMany(CustomerUser::class, 'customer_id');
    }

    /**
     * Get the primary owner of this customer account.
     *
     * The owner is the main contact/account manager who has full administrative
     * privileges for the customer account. Only one user per customer should
     * have is_owner = true.
     */
    public function owner(): HasOne|Customer|Builder
    {
        return $this
            ->hasOne(CustomerUser::class, 'customer_id')
            ->where('is_owner', true);
    }

    /**
     * Get the primary owner's email, falling back to the customer's email if no owner exists.
     *
     * This method provides a reliable way to get the primary contact email for the customer,
     * which can be used for notifications, billing, and other important communications.
     *
     * Accessor for the primary owner's email, falling back to the customer's email if no owner exists.
     * (->primary_contact_email)
     */
    public function getPrimaryContactEmailAttribute(): ?string
    {
        // First try to get the owner's email
        $owner = $this->owner;
        if ($owner && $owner->email) {
            return $owner->email;
        }

        // Fall back to the customer's email field
        return $this->email;
    }
}
