<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $customer_user_id
 * @property int $template_id
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @property-read CustomerUser $customerUser
 * @property-read Template $template
 */
class TemplateFavorite extends Model
{
    protected $fillable = [
        'customer_user_id',
        'template_id',
    ];

    /**
     * Get the customer user that owns the favorite.
     */
    public function customerUser(): BelongsTo
    {
        return $this->belongsTo(CustomerUser::class);
    }

    /**
     * Get the template that is favorited.
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(Template::class);
    }
}
