<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

/**
 * @property int $id
 * @property int $category_id
 * @property string $name
 * @property string $slug
 * @property null|string $description
 * @property string $thumbnail
 * @property null|array<array-key, mixed> $screenshots
 * @property null|array<array-key, mixed> $features
 * @property bool $is_featured
 * @property int $view_count
 * @property string $original_price
 * @property null|string $sale_price
 * @property null|Carbon $created_at
 * @property null|Carbon $updated_at
 * @property-read null|TemplateCategory $category
 * @property-read string $formatted_original_price
 * @property-read string $price
 * @property-read string $formatted_sale_price
 * @property-read string $featured_image_url
 *
 * @method static Builder<static>|Template newModelQuery()
 * @method static Builder<static>|Template newQuery()
 * @method static Builder<static>|Template query()
 * @method static Builder<static>|Template where($column, $operator = null, $value = null, $boolean = 'and')
 * @method static Builder<static>|Template whereCategoryId($value)
 * @method static Builder<static>|Template whereCreatedAt($value)
 * @method static Builder<static>|Template whereDescription($value)
 * @method static Builder<static>|Template whereFeatures($value)
 * @method static Builder<static>|Template whereId($value)
 * @method static Builder<static>|Template whereIsFeatured($value)
 * @method static Builder<static>|Template whereName($value)
 * @method static Builder<static>|Template whereOriginalPrice($value)
 * @method static Builder<static>|Template whereSalePrice($value)
 * @method static Builder<static>|Template whereScreenshots($value)
 * @method static Builder<static>|Template whereSlug($value)
 * @method static Builder<static>|Template whereThumbnail($value)
 * @method static Builder<static>|Template whereUpdatedAt($value)
 * @method static Builder<static>|Template whereViewCount($value)
 * @method static Builder<static>|Template whereNotNull($column)
 *
 * @property null|string $demo_url
 * @property-read bool $has_sale_off
 *
 * @method static Builder<static>|Template whereDemoUrl($value)
 *
 * @property null|Carbon $deleted_at
 * @property-read string $demo_slug
 * @property-read mixed $screenshot_urls
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, Media> $media
 * @property-read null|int $media_count
 * @property-read Collection<int, TemplateFavorite> $templateFavorites
 * @property-read int $favorite_count
 *
 * @method static Builder<static>|Template onlyTrashed()
 * @method static Builder<static>|Template whereDeletedAt($value)
 * @method static Builder<static>|Template withTrashed()
 * @method static Builder<static>|Template withoutTrashed()
 *
 * @property-read Collection<int, \App\Models\CustomerUser> $favoritedByUsers
 * @property-read null|int $favorited_by_users_count
 * @property-read null|int $template_favorites_count
 *
 * @mixin \Eloquent
 */
class Template extends Model implements HasMedia
{
    use InteractsWithMedia;
    use SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'demo_url',
        'category_id',
        'features',
        'is_featured',
        'view_count',
    ];

    /** @var array<string, string> */
    protected $casts = [
        'features' => 'array',
        'is_featured' => 'boolean',
        'view_count' => 'integer',
    ];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection(Media::COLLECTION_TEMPLATE_FEATURED_IMAGE)
            ->singleFile();

        $this->addMediaCollection(Media::COLLECTION_TEMPLATE_SCREENSHOT);
    }

    /**
     * Accessor for featured image url (->featured_image_url)
     */
    public function getFeaturedImageUrlAttribute(): string
    {
        return $this->getFirstMediaUrl(Media::COLLECTION_TEMPLATE_FEATURED_IMAGE);
    }

    /**
     * Accessor for screenshot urls (->screenshot_urls)
     *
     * @return array<int, string>
     */
    public function getScreenshotUrlsAttribute(): array
    {
        return $this->getMedia(Media::COLLECTION_TEMPLATE_SCREENSHOT)->map(fn ($media) => $media->getFullUrl())->toArray();
    }

    /**
     * Get the category that owns the template.
     *
     * @return BelongsTo<TemplateCategory, $this>
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(TemplateCategory::class, 'category_id', 'id');
    }

    /**
     * Accessor for has sale off (->has_sale_off)
     */
    public function getHasSaleOffAttribute(): bool
    {
        if ($this->original_price === null) {
            return false;
        }

        return $this->sale_price < $this->original_price;
    }

    /**
     * Accessor for price (->price)
     *
     * Get the formatted price of the template
     * Returns sale price if available, otherwise original price
     */
    public function getPriceAttribute(): string
    {
        $price = $this->sale_price ?? $this->original_price;

        return number_format((float) $price, 0, ',', '.').'đ';
    }

    /**
     * Get the formatted original price
     * Accessor for formatted original price (->formatted_original_price)
     */
    public function getFormattedOriginalPriceAttribute(): string
    {
        return number_format((float) $this->original_price, 0, ',', '.').'đ';
    }

    /**
     * Accessor for demo slug (->demo_slug)
     */
    public function getDemoSlugAttribute(): string
    {
        return Str::slug($this->name);
    }

    /**
     * Increment the view count of the template.
     */
    public function incrementViewCount(): int
    {
        return $this->increment('view_count');
    }

    /**
     * Get the template favorites.
     *
     * @return HasMany<TemplateFavorite, $this>
     */
    public function templateFavorites(): HasMany
    {
        return $this->hasMany(TemplateFavorite::class);
    }

    /**
     * Get the users who favorited this template.
     *
     * @return BelongsToMany<CustomerUser, $this>
     */
    public function favoritedByUsers(): BelongsToMany
    {
        return $this->belongsToMany(CustomerUser::class, 'template_favorites')
            ->withTimestamps();
    }

    /**
     * Check if template is favorited by a specific user.
     */
    public function isFavoritedBy(?CustomerUser $user): bool
    {
        if (! $user) {
            return false;
        }

        return $this->favoritedByUsers()->where('customer_user_id', $user->id)->exists();
    }

    /**
     * Accessor for favorite count (->favorite_count)
     * Get favorite count for this template.
     */
    public function getFavoriteCountAttribute(): int
    {
        return $this->templateFavorites()->count();
    }
}
