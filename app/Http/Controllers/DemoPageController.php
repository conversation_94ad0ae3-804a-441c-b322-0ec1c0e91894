<?php

namespace App\Http\Controllers;

use App\Http\Requests\DemoPage\StoreDemoPageRequest;
use App\Http\Requests\DemoPage\UpdateDemoPageRequest;
use App\Models\DemoPage;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DemoPageController extends Controller
{
    /**
     * Store a newly created demo page.
     */
    public function store(StoreDemoPageRequest $request): JsonResponse
    {
        try {
            $user = Auth::guard('customer_user')->user();
            
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => __('messages.auth.login_required')
                ], 401);
            }

            $demoPage = DB::transaction(function () use ($request, $user) {
                return DemoPage::create([
                    'customer_user_id' => $user->id,
                    'name' => $request->validated('name'),
                    'content' => $request->validated('content', ''),
                    'status' => 'active',
                ]);
            });

            return response()->json([
                'success' => true,
                'message' => __('messages.demo_page.created_success'),
                'data' => [
                    'id' => $demoPage->id,
                    'name' => $demoPage->name,
                    'slug' => $demoPage->slug,
                    'path' => '/' . $demoPage->slug,
                    'status' => $demoPage->status,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to create demo page', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => __('messages.demo_page.create_failed')
            ], 500);
        }
    }

    /**
     * Update the specified demo page.
     */
    public function update(UpdateDemoPageRequest $request, DemoPage $demoPage): JsonResponse
    {
        try {
            $user = Auth::guard('customer_user')->user();
            
            if (!$user || $demoPage->customer_user_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => __('messages.auth.unauthorized')
                ], 403);
            }

            DB::transaction(function () use ($request, $demoPage) {
                $demoPage->update([
                    'name' => $request->validated('name'),
                    'content' => $request->validated('content', $demoPage->content),
                ]);
            });

            return response()->json([
                'success' => true,
                'message' => __('messages.demo_page.updated_success'),
                'data' => [
                    'id' => $demoPage->id,
                    'name' => $demoPage->name,
                    'slug' => $demoPage->slug,
                    'path' => '/' . $demoPage->slug,
                    'status' => $demoPage->status,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update demo page', [
                'user_id' => Auth::id(),
                'demo_page_id' => $demoPage->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => __('messages.demo_page.update_failed')
            ], 500);
        }
    }

    /**
     * Remove the specified demo page.
     */
    public function destroy(DemoPage $demoPage): JsonResponse
    {
        try {
            $user = Auth::guard('customer_user')->user();
            
            if (!$user || $demoPage->customer_user_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => __('messages.auth.unauthorized')
                ], 403);
            }

            DB::transaction(function () use ($demoPage) {
                $demoPage->delete();
            });

            return response()->json([
                'success' => true,
                'message' => __('messages.demo_page.deleted_success')
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to delete demo page', [
                'user_id' => Auth::id(),
                'demo_page_id' => $demoPage->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => __('messages.demo_page.delete_failed')
            ], 500);
        }
    }

    /**
     * Show the specified demo page.
     */
    public function show(DemoPage $demoPage)
    {
        $user = Auth::guard('customer_user')->user();
        
        if (!$user || $demoPage->customer_user_id !== $user->id) {
            abort(404);
        }

        return view('pages.demo-page.show', compact('demoPage'));
    }
}
