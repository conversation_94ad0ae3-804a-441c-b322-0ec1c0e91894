<?php

namespace App\Http\Controllers;

use App\Models\Template;
use App\Models\TemplateFavorite;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;

class TemplateFavoriteController extends Controller
{
    /**
     * Toggle favorite status for a template.
     */
    public function toggle(Template $template): JsonResponse
    {
        try {
            $user = Auth::guard('customer_user')->user();
            
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => __('messages.auth.login_required')
                ], 401);
            }

            $favorite = TemplateFavorite::where([
                'customer_user_id' => $user->id,
                'template_id' => $template->id,
            ])->first();

            if ($favorite) {
                // Remove from favorites
                $favorite->delete();
                $isFavorited = false;
                $message = __('messages.template.removed_from_favorites');
            } else {
                // Add to favorites
                TemplateFavorite::create([
                    'customer_user_id' => $user->id,
                    'template_id' => $template->id,
                ]);
                $isFavorited = true;
                $message = __('messages.template.added_to_favorites');
            }

            return response()->json([
                'success' => true,
                'is_favorited' => $isFavorited,
                'favorite_count' => $template->getFavoriteCount(),
                'message' => $message
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to toggle template favorite', [
                'user_id' => Auth::id(),
                'template_id' => $template->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => __('messages.template.favorite_failed')
            ], 500);
        }
    }

    /**
     * Show user's favorite templates page.
     */
    public function index(): View
    {
        $user = Auth::guard('customer_user')->user();
        
        if (!$user) {
            abort(401);
        }

        $favoriteTemplates = $user->favoritedTemplates()
            ->with(['category', 'templateFavorites'])
            ->orderBy('template_favorites.created_at', 'desc')
            ->paginate(12);

        return view('pages.user-dashboard.favorites', compact('favoriteTemplates'));
    }

    /**
     * Remove template from favorites.
     */
    public function destroy(Template $template): JsonResponse
    {
        try {
            $user = Auth::guard('customer_user')->user();
            
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => __('messages.auth.login_required')
                ], 401);
            }

            $deleted = TemplateFavorite::where([
                'customer_user_id' => $user->id,
                'template_id' => $template->id,
            ])->delete();

            if ($deleted) {
                return response()->json([
                    'success' => true,
                    'message' => __('messages.template.removed_from_favorites')
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => __('messages.template.not_in_favorites')
            ], 404);

        } catch (\Exception $e) {
            Log::error('Failed to remove template from favorites', [
                'user_id' => Auth::id(),
                'template_id' => $template->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => __('messages.template.favorite_failed')
            ], 500);
        }
    }
}
