<?php

namespace App\Http\Controllers;

use Illuminate\Http\Response;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\RedirectResponse;

class LanguageController extends Controller
{
    /**
     * @param $locale
     *
     * @return RedirectResponse|Response
     */
    public function switch($locale): RedirectResponse|Response
    {
        if (!in_array($locale, ['en', 'vi'])) {
            $locale = 'vi'; // Default to Vietnamese if invalid locale
        }

        // Store the locale in session
        Session::put('locale', $locale);
        
        // Set the application locale
        App::setLocale($locale);

        // If this is an HTMX request, return the updated page
        if (request()->header('HX-Request')) {
            return response()
                ->view('components.language-switcher', [
                    'currentLocale' => $locale,
                    'availableLocales' => [
                        'en' => 'English',
                        'vi' => 'Tiếng Việt',
                    ],
                ])
                ->header('HX-Trigger', 'languageChanged');
        }

        // For regular requests, redirect back
        return redirect()->back();
    }
}
