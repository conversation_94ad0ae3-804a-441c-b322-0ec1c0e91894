<?php

namespace App\Http\Controllers;

use App\Http\Requests\Website\StoreWebsiteRequest;
use App\Http\Requests\Website\UpdateWebsiteRequest;
use App\Http\Requests\Website\WebsiteRegisterRequest;
use App\Jobs\DeployTrial14DaysWebsiteJob;
use App\Models\CustomerUser;
use App\Models\Role;
use App\Models\Website;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class WebsiteController extends Controller
{
    /**
     * Register a new website and user.
     *
     * @throws \Throwable
     */
    public function register(WebsiteRegisterRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            /** @var CustomerUser $user */
            $user = CustomerUser::create([
                'name' => $request->input('name'),
                'email' => $request->input('email'),
                'password' => bcrypt(Str::random(12)),
            ]);

            $user->assignRole(Role::CUSTOMER);

            /** @var Website $website */
            $website = Website::create([
                'customer_user_id' => $user->getKey(),
                'name' => $request->input('name'),
                'phone' => $request->input('phone'),
                'company' => $request->input('company'),
                'purpose' => $request->input('purpose'),
                'domain' => $request->input('domain'),
                'package' => $request->input('package'),
                'detail' => $request->input('details'),
                'status' => Website::STATUS_DEMO,
                'lifecycle_stage' => Website::LIFECYCLE_DEMO,
                'demo_expires_at' => now()->addDays(30),
                'template_id' => $request->input('template_id'),
                'template_name' => $request->input('template_name'),
                'admin_username' => 'cslant',
                'admin_password' => Str::random(12),
                'customer_username' => $request->input('email'),
                'customer_password' => Str::random(12),
            ]);

            DeployTrial14DaysWebsiteJob::dispatch($website)->onQueue('deployments');

            DB::commit();

            $this->guard()->login($user);

            return $this->success(
                data: [
                    'user_id' => $user->getKey(),
                    'website_id' => $website->getKey(),
                ],
                message: __('messages.website.registration_successful'),
                statusCode: 201
            );
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Website registration failed: '.$e->getMessage(), [
                'exception' => $e,
                'input' => $request->except(['_token', 'password']),
            ]);

            return $this->error(
                message: __('messages.errors.website_registration_failed'),
                statusCode: 500,
                errorCode: 'website_registration_failed',
                errors: config('app.debug') ? [
                    'exception' => $e->getMessage(),
                    'file' => $e->getFile().':'.$e->getLine(),
                ] : null
            );
        }
    }

    /**
     * Store a newly created demo website.
     */
    public function store(StoreWebsiteRequest $request): JsonResponse
    {
        try {
            $user = Auth::guard('customer_user')->user();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => __('messages.auth.login_required')
                ], 401);
            }

            $website = DB::transaction(function () use ($request, $user) {
                // Generate domain for demo
                $domain = $this->generateDemoDomain($request->validated('name'));

                return Website::create([
                    'customer_user_id' => $user->id,
                    'name' => $request->validated('name'),
                    'phone' => $user->customer->phone ?? '',
                    'company' => $user->customer->business_name ?? '',
                    'domain' => $domain,
                    'package' => 'demo',
                    'purpose' => $request->validated('purpose', ''),
                    'detail' => $request->validated('detail', ''),
                    'demo_content' => $request->validated('demo_content', ''),
                    'status' => Website::STATUS_ACTIVE,
                    'is_demo' => true,
                ]);
            });

            return response()->json([
                'success' => true,
                'message' => __('messages.website.created_success'),
                'data' => [
                    'id' => $website->id,
                    'name' => $website->name,
                    'domain' => $website->domain,
                    'status' => $website->status,
                    'is_demo' => $website->is_demo,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to create demo website', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => __('messages.website.create_failed')
            ], 500);
        }
    }

    /**
     * Update the specified website.
     */
    public function update(UpdateWebsiteRequest $request, Website $website): JsonResponse
    {
        try {
            $user = Auth::guard('customer_user')->user();

            if (!$user || $website->customer_user_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => __('messages.auth.unauthorized')
                ], 403);
            }

            DB::transaction(function () use ($request, $website) {
                $website->update([
                    'name' => $request->validated('name'),
                    'purpose' => $request->validated('purpose', $website->purpose),
                    'detail' => $request->validated('detail', $website->detail),
                    'demo_content' => $request->validated('demo_content', $website->demo_content),
                ]);
            });

            return response()->json([
                'success' => true,
                'message' => __('messages.website.updated_success'),
                'data' => [
                    'id' => $website->id,
                    'name' => $website->name,
                    'domain' => $website->domain,
                    'status' => $website->status,
                    'is_demo' => $website->is_demo,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update website', [
                'user_id' => Auth::id(),
                'website_id' => $website->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => __('messages.website.update_failed')
            ], 500);
        }
    }

    /**
     * Remove the specified website.
     */
    public function destroy(Website $website): JsonResponse
    {
        try {
            $user = Auth::guard('customer_user')->user();

            if (!$user || $website->customer_user_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => __('messages.auth.unauthorized')
                ], 403);
            }

            DB::transaction(function () use ($website) {
                $website->delete();
            });

            return response()->json([
                'success' => true,
                'message' => __('messages.website.deleted_success')
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to delete website', [
                'user_id' => Auth::id(),
                'website_id' => $website->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => __('messages.website.delete_failed')
            ], 500);
        }
    }

    /**
     * Show the specified website.
     */
    public function show(Website $website)
    {
        $user = Auth::guard('customer_user')->user();

        if (!$user || $website->customer_user_id !== $user->id) {
            abort(404);
        }

        return view('pages.website.show', compact('website'));
    }

    /**
     * Convert demo to trial.
     */
    public function convertToTrial(Website $website): JsonResponse
    {
        try {
            $user = Auth::guard('customer_user')->user();

            if (!$user || $website->customer_user_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => __('messages.auth.unauthorized')
                ], 403);
            }

            if (!$website->isDemo()) {
                return response()->json([
                    'success' => false,
                    'message' => __('messages.website.not_demo')
                ], 400);
            }

            $success = $website->convertToTrial(14);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => __('messages.website.convert_trial_success')
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => __('messages.website.convert_failed')
            ], 400);

        } catch (\Exception $e) {
            Log::error('Failed to convert to trial', [
                'user_id' => Auth::id(),
                'website_id' => $website->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => __('messages.website.convert_failed')
            ], 500);
        }
    }

    /**
     * Convert demo/trial to production.
     */
    public function convertToProduction(Website $website): JsonResponse
    {
        try {
            $user = Auth::guard('customer_user')->user();

            if (!$user || $website->customer_user_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => __('messages.auth.unauthorized')
                ], 403);
            }

            if ($website->isProduction()) {
                return response()->json([
                    'success' => false,
                    'message' => __('messages.website.already_production')
                ], 400);
            }

            $subscriptionData = [
                'website_id' => $website->id,
                'plan_name' => request('plan_name', 'basic'),
                'plan_type' => request('plan_type', 'monthly'),
                'price' => request('price', 29.99),
                'currency' => 'USD',
                'status' => 'active',
                'starts_at' => now(),
                'ends_at' => now()->addMonth(),
                'auto_renew' => true,
            ];

            $success = $website->convertToProduction($subscriptionData);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => __('messages.website.convert_production_success')
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => __('messages.website.convert_failed')
            ], 400);

        } catch (\Exception $e) {
            Log::error('Failed to convert to production', [
                'user_id' => Auth::id(),
                'website_id' => $website->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => __('messages.website.convert_failed')
            ], 500);
        }
    }

    /**
     * Generate demo domain from name.
     */
    private function generateDemoDomain(string $name): string
    {
        $slug = Str::slug($name);
        $domain = $slug . '.demo.cslant.net';
        $counter = 1;

        while (Website::where('domain', $domain)->exists()) {
            $domain = $slug . '-' . $counter . '.demo.cslant.net';
            $counter++;
        }

        return $domain;
    }
}
