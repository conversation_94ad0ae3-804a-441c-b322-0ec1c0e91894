<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use Illuminate\Auth\Events\Verified;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class VerifyCustomerEmailController extends Controller
{
    /**
     * Mark the customer's email address as verified.
     */
    public function __invoke(Request $request, int $id, string $hash): RedirectResponse
    {
        try {
            /** @var Customer $customer */
            $customer = Customer::findOrFail($id);

            // Verify the hash matches the customer's email
            if (! hash_equals($hash, sha1($customer->getEmailForVerification()))) {
                Log::warning('Customer email verification failed: Invalid hash', [
                    'customer_id' => $id,
                    'email' => $customer->email,
                ]);
                
                return redirect()->route('home')
                    ->with('error', __('messages.email.verification_invalid'));
            }

            // Check if already verified
            if ($customer->hasVerifiedEmail()) {
                return redirect()->route('home')
                    ->with('status', __('messages.email.customer_already_verified'));
            }

            // Mark as verified
            if ($customer->markEmailAsVerified()) {
                event(new Verified($customer));
                
                Log::info('Customer email verified successfully', [
                    'customer_id' => $customer->id,
                    'email' => $customer->email,
                ]);
            }

            return redirect()->route('home')
                ->with('success', __('messages.email.customer_verified_success'));

        } catch (\Exception $e) {
            Log::error('Customer email verification error', [
                'customer_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return redirect()->route('home')
                ->with('error', __('messages.email.verification_failed'));
        }
    }

    /**
     * Resend the email verification notification for customer.
     */
    public function resend(Request $request): RedirectResponse
    {
        $request->validate([
            'email' => ['required', 'email', 'exists:customers,email'],
        ]);

        try {
            /** @var Customer $customer */
            $customer = Customer::where('email', $request->email)->firstOrFail();

            if ($customer->hasVerifiedEmail()) {
                return back()->with('status', __('messages.email.customer_already_verified'));
            }

            $customer->sendEmailVerificationNotification();

            Log::info('Customer email verification resent', [
                'customer_id' => $customer->id,
                'email' => $customer->email,
            ]);

            return back()->with('status', __('messages.email.customer_verification_resent'));

        } catch (\Exception $e) {
            Log::error('Customer email verification resend error', [
                'email' => $request->email,
                'error' => $e->getMessage(),
            ]);

            return back()->with('error', __('messages.email.verification_resend_failed'));
        }
    }
}
