<?php

namespace App\Http\Traits;

use Illuminate\Http\JsonResponse;

trait ApiResponse
{
    protected function success(
        mixed $data = null,
        mixed $message = null,
        int $statusCode = 200
    ): JsonResponse {
        $response = [
            'success' => true,
            'data' => $data,
            'code' => $statusCode,
        ];

        if ($message) {
            $response['message'] = $message;
        }

        return response()->json($response, $statusCode);
    }

    /**
     * @param  null|array<string, mixed>  $errors
     */
    protected function error(
        mixed $message = null,
        int $statusCode = 400,
        ?string $errorCode = null,
        ?array $errors = null
    ): JsonResponse {
        $response = [
            'success' => false,
            'code' => $statusCode,
            'message' => $message ?? __('messages.errors.default'),
        ];

        if ($errorCode) {
            $response['error_code'] = $errorCode;
        }

        if ($errors) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $statusCode);
    }

    protected function notFound(mixed $message = null): JsonResponse
    {
        return $this->error(
            message: $message ?? __('messages.errors.not_found'),
            statusCode: 404,
            errorCode: 'not_found'
        );
    }

    /**
     * @param  array<string, mixed>  $errors
     */
    protected function validationError(
        array $errors,
        mixed $message = null
    ): JsonResponse {
        return $this->error(
            message: $message ?? __('messages.errors.validation'),
            statusCode: 422,
            errorCode: 'validation_error',
            errors: $errors
        );
    }

    protected function unauthorized(
        mixed $message = null
    ): JsonResponse {
        return $this->error(
            message: $message ?? __('messages.auth.authentication_failed'),
            statusCode: 401,
            errorCode: 'unauthorized'
        );
    }

    protected function forbidden(
        ?string $message = null
    ): JsonResponse {
        return $this->error(
            message: $message ?? __('messages.errors.forbidden'),
            statusCode: 403,
            errorCode: 'forbidden'
        );
    }

    protected function tooManyRequests(
        mixed $message = null,
        ?int $retryAfter = null
    ): JsonResponse {
        $response = $this->error(
            message: $message ?? __('messages.errors.too_many_requests'),
            statusCode: 429,
            errorCode: 'too_many_requests'
        );

        if ($retryAfter) {
            $response->header('Retry-After', (string) $retryAfter);
        }

        return $response;
    }
}
