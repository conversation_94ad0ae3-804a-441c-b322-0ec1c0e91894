<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class SetLocaleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get the locale from the session or use the default from config
        $locale = Session::get('locale', config('app.locale'));

        // If no locale in session, try to detect from browser or URL path
        if (! Session::has('locale')) {
            $routeName = $request->route()->getName();

            // Check if this is a Vietnamese route (contains .vi.)
            if ($routeName && str_contains($routeName, '.vi.')) {
                $locale = 'vi';
            } elseif ($request->hasHeader('accept-language')) {
                $locale = $request->getPreferredLanguage(['en', 'vi']) ?: $locale;
            }

            /*// Check URL path for language indicators
            $path = $request->path();

            // Check for Vietnamese paths
            if (str_starts_with($path, 'mau-website')
                || str_starts_with($path, 'xem-demo')
                || str_starts_with($path, 'tu-van')
                || str_starts_with($path, 'dang-nhap')
                || str_starts_with($path, 'dang-ky')
            ) {
                $locale = 'vi';
            }
            // Check for English paths
            elseif (
                str_starts_with($path, 'consultation')
                || str_starts_with($path, 'website-templates')
                || str_starts_with($path, 'view-demo')
                || str_starts_with($path, 'login')
                || str_starts_with($path, 'sign-up')
            ) {
                $locale = 'en';
            } // If no path indicators, try browser language
            elseif ($request->hasHeader('accept-language')) {
                $locale = $request->getPreferredLanguage(['en', 'vi']) ?: $locale;
            }*/
        }

        // Ensure the locale is supported
        if (! in_array($locale, ['en', 'vi'])) {
            $locale = config('app.locale');
        }

        // Set the application locale
        App::setLocale($locale);

        // Set the locale in the session for future requests
        Session::put('locale', $locale);

        return $next($request);
    }
}
