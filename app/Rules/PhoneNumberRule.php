<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class PhoneNumberRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Check maximum length first
        if (strlen($value) > 20) {
            $fail(__('messages.validation.phone_max'));
            return;
        }

        // Basic phone number validation that allows international formats
        // - Must start with a plus sign followed by digits (international format)
        // - Or start with digits (national format)
        // - Must be between 8-15 digits in length (minimum 8, maximum 15)
        // - Can contain spaces, hyphens, and parentheses as separators
        if (!preg_match('/^\+?[0-9\s\-\(\)]{8,15}$/', $value)) {
            $fail(__('messages.validation.phone_invalid'));
        }
    }
}
