{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "cslant.net - A Laravel project for CSlant Service.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "awcodes/filament-tiptap-editor": "^3.0", "filament/filament": "^3.3", "filament/forms": "^3.3", "filament/spatie-laravel-media-library-plugin": "^3.2", "laravel-notification-channels/discord": "^1.7", "laravel/framework": "^12.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.10.1", "ralphjsmit/laravel-seo": "^1.7", "resend/resend-php": "^0.18.0", "sentry/sentry-laravel": "^4.15", "spatie/laravel-googletagmanager": "^2.8", "spatie/laravel-medialibrary": "^11.13", "spatie/laravel-permission": "^6.20", "spatie/laravel-settings": "^3.4", "spatie/laravel-sitemap": "^7.3", "spatie/laravel-sluggable": "^3.7", "spatie/laravel-tags": "^4.10", "spatie/ssh": "^1.13"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.5", "fakerphp/faker": "^1.23", "larastan/larastan": "^3.4", "laravel/pail": "^1.2.2", "laravel/pint": "^1.13", "laravel/sail": "^1.41", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "pestphp/pest": "^3.8", "pestphp/pest-plugin-laravel": "^3.2"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/functions.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"analyse": "vendor/bin/phpstan analyse", "a": "vendor/bin/phpstan analyse", "format": "vendor/bin/pint --repair", "f": "vendor/bin/pint --repair", "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"], "test": ["@php artisan config:clear --ansi", "@php artisan test"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}