<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Authentication Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used during authentication for various
    | messages that we need to display to the user. You are free to modify
    | these language lines according to your application's requirements.
    |
    */

    'failed' => 'These credentials do not match our records.',
    'throttle' => 'Too many login attempts. Please try again in :seconds seconds.',
    'login' => 'Login',
    'register' => 'Register',
    'password_reset' => 'Password Reset',
    'password_reset_email' => 'Password Reset Email',
    'password_reset_token' => 'Password Reset Token',
    'password_reset_success' => 'Password Reset Success',
    'password_reset_failed' => 'Password Reset Failed',
    'password_reset_email_sent' => 'Password Reset Email Sent',
    'password_reset_email_sent_message' => 'We have e-mailed your password reset link!',
    'password_reset_email_sent_button' => 'Back to Login',
    
    // Registration form
    'register_title' => 'Create a new account',
    'register_subtitle' => 'Fill in your information to register an account',
    'account_type' => 'Account Type',
    'business_account' => 'Business Account',
    'business_account_desc' => 'For companies and organizations',
    'individual_account' => 'Individual Account',
    'individual_account_desc' => 'For personal use',
    'full_name' => 'Full name',
    'full_name_placeholder' => 'Enter your full name',
    'company_name' => 'Company name',
    'company_name_placeholder' => 'Enter company name',
    'phone' => 'Phone number',
    'phone_placeholder' => 'Enter phone number',
    'email' => 'Email',
    'email_placeholder' => 'Enter email address',
    'password' => 'Password',
    'password_placeholder' => 'Enter password',
    'confirm_password' => 'Confirm password',
    'confirm_password_placeholder' => 'Re-enter password',
    'register_button' => 'Register',
    'registration_failed' => 'Registration failed',
];
